fix: 修复Elasticsearch新增IP字段数据转换缺失问题

🐛 问题描述:
- Elasticsearch中已正确存储新增的IP字段(x_forwarded_for, x_real_ip, cf_connecting_ip)
- 但API返回的日志数据中缺少这些字段
- 导致前端无法显示完整的IP分类信息

🔧 修复内容:
- 在 convertFromElasticsearchLog 函数中添加缺失的IP字段转换
- 确保从Elasticsearch读取数据时包含所有新增的IP字段
- 修复数据写入正常但读取缺失的不一致问题

📝 技术细节:
- 修复文件: model/log_storage_elasticsearch.go
- 修复函数: convertFromElasticsearchLog()
- 新增字段转换:
  * log.XForwardedFor = getString("x_forwarded_for")
  * log.XRealIp = getString("x_real_ip")
  * log.CfConnectingIp = getString("cf_connecting_ip")

✅ 验证结果:
- API现在返回完整的IP字段信息
- 前端可以正确显示IP分类和tooltip
- 智能IP搜索功能完全正常

这个修复确保了Elasticsearch存储后端的数据读写一致性，解决了新增IP字段在API层面缺失的问题。