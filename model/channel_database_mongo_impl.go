package model

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoDatabaseImpl MongoDB数据库实现
type MongoDatabaseImpl struct {
	client        *mongo.Client
	db            *mongo.Database
	channels      *mongo.Collection
	abilities     *mongo.Collection
	channelExs    *mongo.Collection
	channelGroups *mongo.Collection
	counters      *mongo.Collection
}

// NewMongoDatabaseImpl 创建MongoDB数据库实现
func NewMongoDatabaseImpl() (DatabaseInterface, error) {
	connectionString := GetChannelNoSQLConnectionString()
	databaseName := GetChannelNoSQLDatabase()

	clientOptions := options.Client().ApplyURI(connectionString)
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %v", err)
	}

	// 测试连接
	err = client.Ping(context.Background(), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %v", err)
	}

	db := client.Database(databaseName)

	impl := &MongoDatabaseImpl{
		client:        client,
		db:            db,
		channels:      db.Collection("channels"),
		abilities:     db.Collection("abilities"),
		channelExs:    db.Collection("channel_extends"),
		channelGroups: db.Collection("channel_groups"),
		counters:      db.Collection("counters"),
	}

	// 创建索引
	if err := impl.createIndexes(); err != nil {
		logger.SysError("Failed to create MongoDB indexes: " + err.Error())
		// 不返回错误，继续使用
	}

	logger.SysLog("MongoDB connection established successfully")
	return impl, nil
}

// createIndexes 创建必要的索引
func (m *MongoDatabaseImpl) createIndexes() error {
	ctx := context.Background()

	// ========== Channels 集合索引 ==========
	channelIndexes := []mongo.IndexModel{
		// 最关键的索引 - id字段唯一索引
		{Keys: bson.D{bson.E{Key: "id", Value: 1}}, Options: options.Index().SetUnique(true)},

		// 基础字段索引
		{Keys: bson.D{bson.E{Key: "status", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "models", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "name", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "channel_group_id", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "created_time", Value: -1}}},
		{Keys: bson.D{bson.E{Key: "type", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "billing_type", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "base_url", Value: 1}}},

		// 复合索引 - 常用查询组合
		{Keys: bson.D{bson.E{Key: "status", Value: 1}, bson.E{Key: "group", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "models", Value: 1}, bson.E{Key: "status", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "status", Value: 1}, bson.E{Key: "billing_type", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "channel_group_id", Value: 1}, bson.E{Key: "status", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "status", Value: 1}, bson.E{Key: "enabled", Value: 1}}},

		// 排序相关索引
		{Keys: bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "weight", Value: -1}, bson.E{Key: "id", Value: -1}}},
		{Keys: bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "created_time", Value: -1}}},

		// 搜索相关索引
		{Keys: bson.D{bson.E{Key: "name", Value: "text"}, bson.E{Key: "remark", Value: "text"}}},

		// 长分组字段优化索引 - 支持长分组拼接字符串的高效查询
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "models", Value: 1}, bson.E{Key: "status", Value: 1}}},
	}

	// ========== Abilities 集合索引 ==========
	abilityIndexes := []mongo.IndexModel{
		// 复合主键索引
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "model", Value: 1}, bson.E{Key: "channel_id", Value: 1}}, Options: options.Index().SetUnique(true)},

		// 基础字段索引
		{Keys: bson.D{bson.E{Key: "channel_id", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "model", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "sort", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "weight", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "priority", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "billing_type", Value: 1}}},

		// 复合索引 - 常用查询组合
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "model", Value: 1}, bson.E{Key: "enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "channel_id", Value: 1}, bson.E{Key: "enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "model", Value: 1}, bson.E{Key: "enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "enabled", Value: 1}, bson.E{Key: "billing_type", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "enabled", Value: 1}, bson.E{Key: "function_call_enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "enabled", Value: 1}, bson.E{Key: "image_supported", Value: 1}}},

		// 排序和权重相关索引
		{Keys: bson.D{bson.E{Key: "priority", Value: -1}, bson.E{Key: "weight", Value: -1}, bson.E{Key: "sort", Value: -1}}},
	}

	// ========== Channel Extends 集合索引 ==========
	channelExtendIndexes := []mongo.IndexModel{
		// 主键索引
		{Keys: bson.D{bson.E{Key: "id", Value: 1}}, Options: options.Index().SetUnique(true)},

		// 外键索引 - 最重要的索引
		{Keys: bson.D{bson.E{Key: "channel_id", Value: 1}}, Options: options.Index().SetUnique(true)},

		// 功能相关索引
		{Keys: bson.D{bson.E{Key: "transparent_proxy_enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "filter_stream_ad", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "filter_non_stream_ad", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "request_token_limit_enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "claude_stream_enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "keyword_error_enabled", Value: 1}}},

		// 复合索引
		{Keys: bson.D{bson.E{Key: "channel_id", Value: 1}, bson.E{Key: "transparent_proxy_enabled", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "request_token_limit_enabled", Value: 1}, bson.E{Key: "min_request_token_count", Value: 1}}},
	}

	// ========== Channel Groups 集合索引 ==========
	channelGroupIndexes := []mongo.IndexModel{
		// 主键索引
		{Keys: bson.D{bson.E{Key: "id", Value: 1}}, Options: options.Index().SetUnique(true)},

		// 基础字段索引
		{Keys: bson.D{bson.E{Key: "status", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "name", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "type", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "created_time", Value: -1}}},
		{Keys: bson.D{bson.E{Key: "models", Value: 1}}},

		// 复合索引
		{Keys: bson.D{bson.E{Key: "status", Value: 1}, bson.E{Key: "group", Value: 1}}},
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "models", Value: 1}}},

		// 排序索引
		{Keys: bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "id", Value: -1}}},

		// 搜索索引 - 文本搜索索引，支持长分组字符串的全文搜索
		{Keys: bson.D{bson.E{Key: "name", Value: "text"}, bson.E{Key: "group", Value: "text"}}},

		// 长分组字段的前缀索引优化（MongoDB会自动优化，但显式创建有助于性能）
		{Keys: bson.D{bson.E{Key: "group", Value: 1}, bson.E{Key: "status", Value: 1}, bson.E{Key: "created_time", Value: -1}}},
	}

	// ========== Counters 集合索引 ==========
	counterIndexes := []mongo.IndexModel{
		// _id字段默认就是唯一索引，不需要额外设置
		// {Keys: bson.D{bson.E{Key: "_id", Value: 1}}, Options: options.Index().SetUnique(true)},
	}

	// 创建所有索引
	collections := []struct {
		collection *mongo.Collection
		indexes    []mongo.IndexModel
		name       string
	}{
		{m.channels, channelIndexes, "channels"},
		{m.abilities, abilityIndexes, "abilities"},
		{m.channelExs, channelExtendIndexes, "channel_extends"},
		{m.channelGroups, channelGroupIndexes, "channel_groups"},
		{m.counters, counterIndexes, "counters"},
	}

	for _, coll := range collections {
		if len(coll.indexes) > 0 {
			logger.SysLog(fmt.Sprintf("Creating indexes for %s collection...", coll.name))

			// 特殊处理abilities集合的索引冲突
			if coll.name == "abilities" {
				err := m.handleAbilitiesIndexes(ctx, coll.collection, coll.indexes)
				if err != nil {
					logger.SysError(fmt.Sprintf("Failed to create indexes for %s: %v", coll.name, err))
					return err
				}
			} else {
				_, err := coll.collection.Indexes().CreateMany(ctx, coll.indexes)
				if err != nil {
					logger.SysError(fmt.Sprintf("Failed to create indexes for %s: %v", coll.name, err))
					return err
				}
			}

			logger.SysLog(fmt.Sprintf("Successfully created %d indexes for %s collection", len(coll.indexes), coll.name))
		}
	}

	logger.SysLog("All MongoDB indexes created successfully")
	return nil
}

// handleAbilitiesIndexes 处理abilities集合的索引冲突
func (m *MongoDatabaseImpl) handleAbilitiesIndexes(ctx context.Context, collection *mongo.Collection, indexes []mongo.IndexModel) error {
	// 获取现有索引
	cursor, err := collection.Indexes().List(ctx)
	if err != nil {
		return fmt.Errorf("failed to list existing indexes: %w", err)
	}
	defer cursor.Close(ctx)

	existingIndexes := make(map[string]bool)
	for cursor.Next(ctx) {
		var index bson.M
		if err := cursor.Decode(&index); err != nil {
			continue
		}
		if name, ok := index["name"].(string); ok {
			existingIndexes[name] = true
		}
	}

	// 检查是否存在冲突的索引名称
	conflictIndexName := "group_1_model_1_channel_id_1"
	if existingIndexes[conflictIndexName] {
		logger.SysLog("Found conflicting index, dropping it: " + conflictIndexName)
		_, err := collection.Indexes().DropOne(ctx, conflictIndexName)
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to drop conflicting index %s: %v", conflictIndexName, err))
			// 继续执行，不返回错误
		}
	}

	// 逐个创建索引，跳过已存在的索引
	for _, index := range indexes {
		_, err := collection.Indexes().CreateOne(ctx, index)
		if err != nil {
			// 检查是否是索引已存在的错误
			if strings.Contains(err.Error(), "IndexKeySpecsConflict") ||
				strings.Contains(err.Error(), "already exists") {
				logger.SysLog("Index already exists, skipping...")
				continue
			}
			return fmt.Errorf("failed to create index: %w", err)
		}
	}

	return nil
}

// Channel operations - 核心高频操作优化
func (m *MongoDatabaseImpl) GetChannelById(id int, selectAll bool) (*Channel, error) {
	ctx := context.Background()
	filter := bson.M{"id": id}

	var projection bson.M
	if !selectAll {
		projection = bson.M{"key": 0}
	}

	opts := options.FindOne()
	if projection != nil {
		opts.SetProjection(projection)
	}

	var channel Channel
	err := m.channels.FindOne(ctx, filter, opts).Decode(&channel)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("channel not found")
		}
		return nil, err
	}

	return &channel, nil
}

// UpdateChannelStatusById 更新渠道状态并同步更新能力状态
func (m *MongoDatabaseImpl) UpdateChannelStatusById(id int, status int) error {
	ctx := context.Background()

	// 尝试使用事务，如果失败则使用普通操作
	session, err := m.client.StartSession()
	if err != nil {
		return m.updateChannelStatusWithoutTransaction(ctx, id, status)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 更新渠道状态
		channelFilter := bson.M{"id": id}
		channelUpdate := bson.M{"$set": bson.M{"status": status}}
		_, err := m.channels.UpdateOne(sessCtx, channelFilter, channelUpdate)
		if err != nil {
			return nil, err
		}

		// 批量更新对应的能力状态 - 单次操作
		abilityFilter := bson.M{"channel_id": id}
		abilityUpdate := bson.M{"$set": bson.M{"enabled": status == ChannelStatusEnabled}}
		_, err = m.abilities.UpdateMany(sessCtx, abilityFilter, abilityUpdate)

		return nil, err
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		// 如果事务失败（可能是因为单机MongoDB不支持事务），使用普通操作
		if isTransactionNotSupportedError(err) {
			return m.updateChannelStatusWithoutTransaction(ctx, id, status)
		}
		return err
	}
	return nil
}

// updateChannelStatusWithoutTransaction 不使用事务的渠道状态更新
func (m *MongoDatabaseImpl) updateChannelStatusWithoutTransaction(ctx context.Context, id int, status int) error {
	// 更新渠道状态
	channelFilter := bson.M{"id": id}
	channelUpdate := bson.M{"$set": bson.M{"status": status}}
	_, err := m.channels.UpdateOne(ctx, channelFilter, channelUpdate)
	if err != nil {
		return err
	}

	// 批量更新对应的能力状态
	abilityFilter := bson.M{"channel_id": id}
	abilityUpdate := bson.M{"$set": bson.M{"enabled": status == ChannelStatusEnabled}}
	_, err = m.abilities.UpdateMany(ctx, abilityFilter, abilityUpdate)
	return err
}

// UpdateChannelAbilityStatusByIdReturnErr 更新特定能力状态 - 重点优化
func (m *MongoDatabaseImpl) UpdateChannelAbilityStatusByIdReturnErr(id int, requestModel string, status int) error {
	ctx := context.Background()

	// 先检查渠道状态
	var channel Channel
	err := m.channels.FindOne(ctx, bson.M{"id": id}).Decode(&channel)
	if err != nil {
		return err
	}

	// 如果是手动禁用状态，直接返回
	if channel.Status == common.ChannelStatusManuallyDisabled {
		return errors.New("channel is manually disabled")
	}

	// 尝试使用事务，如果失败则使用普通操作
	session, err := m.client.StartSession()
	if err != nil {
		return m.updateChannelAbilityStatusWithoutTransaction(ctx, id, requestModel, status)
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sc mongo.SessionContext) (interface{}, error) {
		// 更新指定的 Ability 状态
		abilityFilter := bson.M{"channel_id": id, "model": requestModel}
		abilityUpdate := bson.M{"$set": bson.M{"enabled": status == common.ChannelStatusEnabled}}
		_, err := m.abilities.UpdateMany(sc, abilityFilter, abilityUpdate)
		if err != nil {
			logger.SysError("failed to update ability status: " + err.Error())
			return nil, err
		}

		var newStatus int
		if status != common.ChannelStatusEnabled {
			// 检查是否所有 Ability 都被禁用
			enabledCount, err := m.abilities.CountDocuments(sc, bson.M{"channel_id": id, "enabled": true})
			if err != nil {
				logger.SysError("failed to count enabled abilities: " + err.Error())
				return nil, err
			}

			if enabledCount == 0 {
				newStatus = common.ChannelStatusAutoDisabled
			} else {
				newStatus = common.ChannelStatusPartiallyAutoDisabled
			}

			channelFilter := bson.M{"id": id}
			channelUpdate := bson.M{"$set": bson.M{"status": newStatus}}
			_, err = m.channels.UpdateOne(sc, channelFilter, channelUpdate)
			return nil, err
		} else {
			// 查询除了当前更新的 Ability 之外的所有 Ability
			disabledCount, err := m.abilities.CountDocuments(sc, bson.M{"channel_id": id, "model": bson.M{"$ne": requestModel}, "enabled": false})
			if err != nil {
				logger.SysError("failed to count disabled abilities: " + err.Error())
				return nil, err
			}

			if disabledCount == 0 {
				newStatus = common.ChannelStatusEnabled
			} else {
				newStatus = common.ChannelStatusPartiallyAutoDisabled
			}

			channelFilter := bson.M{"id": id}
			channelUpdate := bson.M{"$set": bson.M{"status": newStatus}}
			_, err = m.channels.UpdateOne(sc, channelFilter, channelUpdate)
			return nil, err
		}
	})

	if err != nil {
		// 如果事务失败（可能是因为单机MongoDB不支持事务），使用普通操作
		if isTransactionNotSupportedError(err) {
			return m.updateChannelAbilityStatusWithoutTransaction(ctx, id, requestModel, status)
		}
		return err
	}
	return nil
}

// updateChannelAbilityStatusWithoutTransaction 不使用事务的能力状态更新
func (m *MongoDatabaseImpl) updateChannelAbilityStatusWithoutTransaction(ctx context.Context, id int, requestModel string, status int) error {
	// 更新指定的 Ability 状态
	abilityFilter := bson.M{"channel_id": id, "model": requestModel}
	abilityUpdate := bson.M{"$set": bson.M{"enabled": status == common.ChannelStatusEnabled}}
	_, err := m.abilities.UpdateMany(ctx, abilityFilter, abilityUpdate)
	if err != nil {
		logger.SysError("failed to update ability status: " + err.Error())
		return err
	}

	var newStatus int
	if status != common.ChannelStatusEnabled {
		// 检查是否所有 Ability 都被禁用
		enabledCount, err := m.abilities.CountDocuments(ctx, bson.M{"channel_id": id, "enabled": true})
		if err != nil {
			logger.SysError("failed to count enabled abilities: " + err.Error())
			return err
		}

		if enabledCount == 0 {
			newStatus = common.ChannelStatusAutoDisabled
		} else {
			newStatus = common.ChannelStatusPartiallyAutoDisabled
		}

		channelFilter := bson.M{"id": id}
		channelUpdate := bson.M{"$set": bson.M{"status": newStatus}}
		_, err = m.channels.UpdateOne(ctx, channelFilter, channelUpdate)
		return err
	} else {
		// 查询除了当前更新的 Ability 之外的所有 Ability
		disabledCount, err := m.abilities.CountDocuments(ctx, bson.M{"channel_id": id, "model": bson.M{"$ne": requestModel}, "enabled": false})
		if err != nil {
			logger.SysError("failed to count disabled abilities: " + err.Error())
			return err
		}

		if disabledCount == 0 {
			newStatus = common.ChannelStatusEnabled
		} else {
			newStatus = common.ChannelStatusPartiallyAutoDisabled
		}

		channelFilter := bson.M{"id": id}
		channelUpdate := bson.M{"$set": bson.M{"status": newStatus}}
		_, err = m.channels.UpdateOne(ctx, channelFilter, channelUpdate)
		return err
	}
}

// UpdateChannelDisableReasonById 更新禁用原因
func (m *MongoDatabaseImpl) UpdateChannelDisableReasonById(id int, disableReason string) error {
	ctx := context.Background()
	filter := bson.M{"id": id}
	update := bson.M{"$set": bson.M{"disableReason": disableReason}}

	_, err := m.channels.UpdateOne(ctx, filter, update)
	return err
}

// UpdateChannelUsedQuota 更新使用配额 - 高频操作优化
func (m *MongoDatabaseImpl) UpdateChannelUsedQuota(id int, quota int64) error {
	ctx := context.Background()
	filter := bson.M{"id": id}
	update := bson.M{"$inc": bson.M{"used_quota": quota}}

	_, err := m.channels.UpdateOne(ctx, filter, update)
	return err
}

// 其他核心方法的简化实现...
func (m *MongoDatabaseImpl) GetAllChannels(startIdx int, num int, orderByStr string, selectAll bool, id int, name string, key string,
	group string, models string, channelGroupId int, status int, billingType int, baseUrl string,
	overFrequencyAutoDisable string, scope string, disableReason string) ([]*Channel, error) {

	ctx := context.Background()
	filter := bson.M{}
	opts := options.Find()

	// 构建查询条件
	if id != 0 {
		filter["id"] = id
	}
	if name != "" {
		filter["$or"] = []bson.M{
			{"name": bson.M{"$regex": name, "$options": "i"}},
			{"remark": bson.M{"$regex": name, "$options": "i"}},
		}
	}
	if key != "" {
		filter["key"] = key
	}
	if group != "" {
		// 处理逗号分隔的组
		groups := strings.Split(group, ",")
		groupConditions := make([]bson.M, 0, len(groups))
		for _, g := range groups {
			groupConditions = append(groupConditions, bson.M{"group": bson.M{"$regex": g, "$options": "i"}})
		}
		if len(groupConditions) > 1 {
			filter["$and"] = groupConditions
		} else if len(groupConditions) == 1 {
			filter["group"] = bson.M{"$regex": groups[0], "$options": "i"}
		}
	}
	if models != "" {
		// 处理逗号分隔的模型
		modelList := strings.Split(models, ",")
		modelConditions := make([]bson.M, 0, len(modelList))
		for _, model := range modelList {
			modelConditions = append(modelConditions, bson.M{"models": bson.M{"$regex": model, "$options": "i"}})
		}
		if len(modelConditions) > 1 {
			if filter["$and"] != nil {
				filter["$and"] = append(filter["$and"].([]bson.M), modelConditions...)
			} else {
				filter["$and"] = modelConditions
			}
		} else if len(modelConditions) == 1 {
			filter["models"] = bson.M{"$regex": modelList[0], "$options": "i"}
		}
	}
	if channelGroupId != 0 {
		if channelGroupId == -1 {
			filter["$or"] = []bson.M{
				{"channel_group_id": 0},
				{"channel_group_id": -1},
				{"channel_group_id": bson.M{"$exists": false}},
			}
		} else {
			filter["channel_group_id"] = channelGroupId
		}
	}
	if status != 0 {
		filter["status"] = status
	}
	if billingType != 0 {
		filter["billing_type"] = billingType
	}
	if baseUrl != "" {
		filter["base_url"] = baseUrl
	}
	if overFrequencyAutoDisable != "" {
		if overFrequencyAutoDisable == "true" {
			filter["over_frequency_auto_disable"] = true
		} else if overFrequencyAutoDisable == "false" {
			filter["over_frequency_auto_disable"] = false
		}
	}
	if disableReason != "" {
		// 根据不同的错误类型进行匹配
		switch disableReason {
		case "account_deactivated":
			filter["disableReason"] = bson.M{"$regex": "account.*deactivated", "$options": "i"}
		case "quota_exceeded":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "quota.*exceeded", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "insufficient.*quota", "$options": "i"}},
			}
		case "rate_limit_exceeded":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "rate.*limit", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "too.*many.*requests", "$options": "i"}},
			}
		case "invalid_key":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "invalid.*key", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "incorrect.*api.*key", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "authentication.*failed", "$options": "i"}},
			}
		case "connection_error":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "connection.*failed", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "timeout", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "refused", "$options": "i"}},
			}
		case "internal_server_error":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "internal.*server.*error", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "500", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "server.*error", "$options": "i"}},
			}
		default:
			filter["disableReason"] = bson.M{"$regex": disableReason, "$options": "i"}
		}
	}

	// 处理排序
	if orderByStr != "" {
		// 解析排序字符串，例如 "id desc" 或 "name asc"
		parts := strings.Fields(orderByStr)
		if len(parts) >= 2 {
			field := parts[0]
			direction := 1
			if strings.ToLower(parts[1]) == "desc" {
				direction = -1
			}
			opts.SetSort(bson.D{bson.E{Key: field, Value: direction}})
		} else {
			// 默认排序
			opts.SetSort(bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "weight", Value: -1}, bson.E{Key: "id", Value: -1}})
		}
	} else {
		// 默认排序
		opts.SetSort(bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "weight", Value: -1}, bson.E{Key: "id", Value: -1}})
	}

	if !selectAll {
		opts.SetProjection(bson.M{"key": 0})
	}

	if num != 0 {
		opts.SetLimit(int64(num))
		opts.SetSkip(int64(startIdx))
	}

	cursor, err := m.channels.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var channels []*Channel
	for cursor.Next(ctx) {
		var channel Channel
		if err := cursor.Decode(&channel); err != nil {
			return nil, err
		}
		channels = append(channels, &channel)
	}

	return channels, cursor.Err()
}

// 实现其他接口方法的占位符 - 生产环境需要完整实现
func (m *MongoDatabaseImpl) SearchChannels(startIdx int, num int, keyword string, channelGroupId int) ([]*Channel, error) {
	ctx := context.Background()
	filter := bson.M{}
	opts := options.Find()

	// 处理渠道组ID过滤
	if channelGroupId != 0 {
		if channelGroupId == -1 {
			// 特殊处理传-1默认查询没有加挂渠道组的渠道
			filter["$or"] = []bson.M{
				{"channel_group_id": 0},
				{"channel_group_id": -1},
				{"channel_group_id": bson.M{"$exists": false}},
			}
		} else {
			filter["channel_group_id"] = channelGroupId
		}
	}

	// 构建关键词搜索条件
	if keyword != "" {
		// 尝试将keyword转换为数字用于ID搜索
		var keywordInt int
		if _, err := fmt.Sscanf(keyword, "%d", &keywordInt); err == nil {
			filter["$or"] = []bson.M{
				{"id": keywordInt},
				{"name": bson.M{"$regex": keyword, "$options": "i"}},
				{"key": keyword},
				{"group": bson.M{"$regex": keyword, "$options": "i"}},
			}
		} else {
			filter["$or"] = []bson.M{
				{"name": bson.M{"$regex": keyword, "$options": "i"}},
				{"key": keyword},
				{"group": bson.M{"$regex": keyword, "$options": "i"}},
			}
		}
	}

	// 设置排序、分页和投影
	opts.SetSort(bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "id", Value: -1}})
	opts.SetLimit(int64(num))
	opts.SetSkip(int64(startIdx))
	opts.SetProjection(bson.M{"key": 0}) // 不返回key字段

	cursor, err := m.channels.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var channels []*Channel
	for cursor.Next(ctx) {
		var channel Channel
		if err := cursor.Decode(&channel); err != nil {
			return nil, err
		}
		channels = append(channels, &channel)
	}

	return channels, cursor.Err()
}

func (m *MongoDatabaseImpl) CountChannels(keyword string, id int, name string, key string, group string, models string, channelGroupId int, status int, billingType int, baseUrl string, overFrequencyAutoDisable string, disableReason string) (int64, error) {
	ctx := context.Background()
	filter := bson.M{}

	// 构建查询条件
	if id != 0 {
		filter["id"] = id
	}
	if name != "" {
		filter["$or"] = []bson.M{
			{"name": bson.M{"$regex": name, "$options": "i"}},
			{"remark": bson.M{"$regex": name, "$options": "i"}},
		}
	}
	if key != "" {
		filter["key"] = key
	}
	if group != "" {
		// 处理逗号分隔的组
		groups := strings.Split(group, ",")
		groupConditions := make([]bson.M, 0, len(groups))
		for _, g := range groups {
			groupConditions = append(groupConditions, bson.M{"group": bson.M{"$regex": g, "$options": "i"}})
		}
		if len(groupConditions) > 1 {
			filter["$and"] = groupConditions
		} else if len(groupConditions) == 1 {
			filter["group"] = bson.M{"$regex": groups[0], "$options": "i"}
		}
	}
	if models != "" {
		// 处理逗号分隔的模型
		modelList := strings.Split(models, ",")
		modelConditions := make([]bson.M, 0, len(modelList))
		for _, model := range modelList {
			modelConditions = append(modelConditions, bson.M{"models": bson.M{"$regex": model, "$options": "i"}})
		}
		if len(modelConditions) > 1 {
			filter["$and"] = append(filter["$and"].([]bson.M), modelConditions...)
		} else if len(modelConditions) == 1 {
			filter["models"] = bson.M{"$regex": modelList[0], "$options": "i"}
		}
	}
	if channelGroupId != 0 {
		if channelGroupId == -1 {
			filter["$or"] = []bson.M{
				{"channel_group_id": 0},
				{"channel_group_id": -1},
				{"channel_group_id": bson.M{"$exists": false}},
			}
		} else {
			filter["channel_group_id"] = channelGroupId
		}
	}
	if status != 0 {
		filter["status"] = status
	}
	if billingType != 0 {
		filter["billing_type"] = billingType
	}
	if baseUrl != "" {
		filter["base_url"] = baseUrl
	}
	if overFrequencyAutoDisable != "" {
		filter["over_frequency_auto_disable"] = overFrequencyAutoDisable
	}
	if disableReason != "" {
		// 根据不同的错误类型进行匹配
		switch disableReason {
		case "account_deactivated":
			filter["disableReason"] = bson.M{"$regex": "account.*deactivated", "$options": "i"}
		case "quota_exceeded":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "quota.*exceeded", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "insufficient.*quota", "$options": "i"}},
			}
		case "rate_limit_exceeded":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "rate.*limit", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "too.*many.*requests", "$options": "i"}},
			}
		case "invalid_key":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "invalid.*key", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "incorrect.*api.*key", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "authentication.*failed", "$options": "i"}},
			}
		case "connection_error":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "connection.*failed", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "timeout", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "refused", "$options": "i"}},
			}
		case "internal_server_error":
			filter["$or"] = []bson.M{
				{"disableReason": bson.M{"$regex": "internal.*server.*error", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "500", "$options": "i"}},
				{"disableReason": bson.M{"$regex": "server.*error", "$options": "i"}},
			}
		default:
			filter["disableReason"] = bson.M{"$regex": disableReason, "$options": "i"}
		}
	}
	if keyword != "" {
		// 尝试将keyword转换为数字用于ID搜索
		var keywordInt int
		if _, err := fmt.Sscanf(keyword, "%d", &keywordInt); err == nil {
			filter["$or"] = []bson.M{
				{"id": keywordInt},
				{"name": bson.M{"$regex": keyword, "$options": "i"}},
				{"key": keyword},
				{"group": bson.M{"$regex": keyword, "$options": "i"}},
			}
		} else {
			filter["$or"] = []bson.M{
				{"name": bson.M{"$regex": keyword, "$options": "i"}},
				{"key": keyword},
				{"group": bson.M{"$regex": keyword, "$options": "i"}},
			}
		}
	}

	count, err := m.channels.CountDocuments(ctx, filter)
	return count, err
}

// 其他方法的占位符实现...
func (m *MongoDatabaseImpl) InsertChannel(channel *Channel) error {
	ctx := context.Background()

	// 生成自增ID
	if channel.Id == 0 {
		// 获取下一个ID
		nextId, err := m.getNextSequenceValue("channel_id")
		if err != nil {
			return err
		}
		channel.Id = nextId
	}

	// 设置默认值（MongoDB 不支持 schema 级别的默认值）
	// 根据 Channel 结构体中的 GORM default 标签设置默认值
	if channel.Status == 0 {
		channel.Status = 1 // default:1
	}
	if channel.ChannelGroupId == 0 {
		channel.ChannelGroupId = 0 // default:0 (已经是默认值)
	}
	if channel.Type == 0 {
		channel.Type = 0 // default:0 (已经是默认值)
	}
	if channel.Weight == nil {
		defaultWeight := uint(0) // default:0
		channel.Weight = &defaultWeight
	}
	if channel.BaseURL == nil {
		defaultBaseURL := "" // default:''
		channel.BaseURL = &defaultBaseURL
	}
	if channel.Group == "" {
		channel.Group = "default" // default:'default'
	}
	if channel.UsedQuota == 0 {
		channel.UsedQuota = 0 // default:0 (已经是默认值)
	}
	if channel.Sort == nil {
		defaultSort := 0 // default:0
		channel.Sort = &defaultSort
	}
	if channel.OverFrequencyAutoDisable == nil {
		defaultOverFrequency := false // default:0
		channel.OverFrequencyAutoDisable = &defaultOverFrequency
	}
	if channel.RetryInterval == nil {
		defaultRetryInterval := 300 // default:300
		channel.RetryInterval = &defaultRetryInterval
	}
	if channel.UndeadModeEnabled == nil {
		defaultUndeadMode := false // default:0
		channel.UndeadModeEnabled = &defaultUndeadMode
	}
	if channel.Priority == nil {
		defaultPriority := int64(0) // default:0
		channel.Priority = &defaultPriority
	}
	if channel.NonStrictTestMode == nil {
		defaultNonStrictTest := false // default:0
		channel.NonStrictTestMode = &defaultNonStrictTest
	}
	if channel.BillingType == 0 {
		channel.BillingType = 1 // default:1
	}
	if channel.FunctionCallEnabled == nil {
		defaultFunctionCall := true // default:1
		channel.FunctionCallEnabled = &defaultFunctionCall
	}
	if channel.ImageSupported == nil {
		defaultImageSupported := true // default:1
		channel.ImageSupported = &defaultImageSupported
	}
	if channel.ImageInMarkdown == nil {
		defaultImageInMarkdown := false // default:0
		channel.ImageInMarkdown = &defaultImageInMarkdown
	}

	// 使用事务来同时插入渠道和创建abilities
	session, err := m.client.StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		// 插入渠道
		_, err := m.channels.InsertOne(sc, channel)
		if err != nil {
			return err
		}

		// 创建abilities
		return m.createAbilitiesForChannel(channel)
	})

	return err
}

func (m *MongoDatabaseImpl) UpdateChannel(channel *Channel) error {
	ctx := context.Background()
	filter := bson.M{"id": channel.Id}

	// 构建更新文档，只包含非零值字段
	update := bson.M{}

	// 基本字段
	if channel.ChannelGroupId != 0 {
		update["channel_group_id"] = channel.ChannelGroupId
	}
	if channel.Type != 0 {
		update["type"] = channel.Type
	}
	if channel.Key != "" {
		update["key"] = channel.Key
	}
	if channel.OpenAIOrganization != nil {
		update["openai_organization"] = channel.OpenAIOrganization
	}
	if channel.Status != 0 {
		update["status"] = channel.Status
	}
	if channel.Name != "" {
		update["name"] = channel.Name
	}
	if channel.Remark != nil {
		update["remark"] = channel.Remark
	}
	if channel.Weight != nil {
		update["weight"] = channel.Weight
	}
	if channel.CreatedTime != 0 {
		update["created_time"] = channel.CreatedTime
	}
	if channel.TestTime != 0 {
		update["test_time"] = channel.TestTime
	}
	if channel.ResponseTime != 0 {
		update["response_time"] = channel.ResponseTime
	}
	if channel.BaseURL != nil {
		update["base_url"] = channel.BaseURL
	}
	if channel.Other != nil {
		update["other"] = channel.Other
	}
	if channel.Balance != 0 {
		update["balance"] = channel.Balance
	}
	if channel.BalanceUpdatedTime != 0 {
		update["balance_updated_time"] = channel.BalanceUpdatedTime
	}
	if channel.CustomBalanceLimit != 0 {
		update["custom_balance_limit"] = channel.CustomBalanceLimit
	}
	if channel.Models != "" {
		update["models"] = channel.Models
	}
	if channel.Group != "" {
		update["group"] = channel.Group
	}
	if channel.UsedQuota != 0 {
		update["used_quota"] = channel.UsedQuota
	}
	if channel.ModelMapping != nil {
		update["model_mapping"] = channel.ModelMapping
	}
	if channel.ExcludedFields != nil {
		update["excluded_fields"] = channel.ExcludedFields
	}
	if channel.ExcludedResponseFields != nil {
		update["excluded_response_fields"] = channel.ExcludedResponseFields
	}
	if channel.ExtraFields != nil {
		update["extra_fields"] = channel.ExtraFields
	}
	if channel.Sort != nil {
		update["sort"] = channel.Sort
	}
	if channel.OverFrequencyAutoDisable != nil {
		update["overFrequencyAutoDisable"] = channel.OverFrequencyAutoDisable
	}
	if channel.RetryInterval != nil {
		update["retryInterval"] = channel.RetryInterval
	}
	if channel.UndeadModeEnabled != nil {
		update["undeadModeEnabled"] = channel.UndeadModeEnabled
	}
	if channel.Priority != nil {
		update["priority"] = channel.Priority
	}
	if channel.NonStrictTestMode != nil {
		update["nonStrictTestMode"] = channel.NonStrictTestMode
	}
	if channel.TestRequestBody != nil {
		update["testRequestBody"] = channel.TestRequestBody
	}
	if channel.DisableReason != nil {
		update["disableReason"] = channel.DisableReason
	}
	if channel.BillingType != 0 {
		update["billing_type"] = channel.BillingType
	}
	if channel.FunctionCallEnabled != nil {
		update["function_call_enabled"] = channel.FunctionCallEnabled
	}
	if channel.ImageSupported != nil {
		update["image_supported"] = channel.ImageSupported
	}
	if channel.ImageInMarkdown != nil {
		update["image_in_markdown"] = channel.ImageInMarkdown
	}
	if channel.Config != "" {
		update["config"] = channel.Config
	}
	if channel.SystemPrompt != nil {
		update["system_prompt"] = channel.SystemPrompt
	}

	// 如果没有字段需要更新，直接返回
	if len(update) == 0 {
		return nil
	}

	// 使用事务来同时更新渠道和相关的abilities
	session, err := m.client.StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		// 更新渠道
		_, err := m.channels.UpdateOne(sc, filter, bson.M{"$set": update})
		if err != nil {
			return err
		}

		// 获取更新后的渠道信息
		var updatedChannel Channel
		err = m.channels.FindOne(sc, filter).Decode(&updatedChannel)
		if err != nil {
			return err
		}

		// 删除旧的abilities
		_, err = m.abilities.DeleteMany(sc, bson.M{"channel_id": channel.Id})
		if err != nil {
			return err
		}

		// 创建新的abilities
		if updatedChannel.Models != "" && updatedChannel.Group != "" {
			models := strings.Split(updatedChannel.Models, ",")
			groups := strings.Split(updatedChannel.Group, ",")

			var abilities []interface{}
			for _, model := range models {
				for _, group := range groups {
					// 清理字符串
					model = strings.TrimSpace(model)
					group = strings.TrimSpace(group)
					if model == "" || group == "" {
						continue
					}

					enabled := updatedChannel.Status == 1 // ChannelStatusEnabled
					ability := Ability{
						Group:               group,
						Model:               model,
						ChannelId:           updatedChannel.Id,
						Enabled:             &enabled,
						Sort:                updatedChannel.Sort,
						Weight:              updatedChannel.Weight,
						Priority:            updatedChannel.Priority,
						BillingType:         updatedChannel.BillingType,
						FunctionCallEnabled: updatedChannel.FunctionCallEnabled,
						ImageSupported:      updatedChannel.ImageSupported,
					}
					abilities = append(abilities, ability)
				}
			}

			if len(abilities) > 0 {
				_, err = m.abilities.InsertMany(sc, abilities)
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	return err
}

func (m *MongoDatabaseImpl) DeleteChannel(channel *Channel) error {
	ctx := context.Background()
	filter := bson.M{"id": channel.Id}
	_, err := m.channels.DeleteOne(ctx, filter)
	return err
}

func (m *MongoDatabaseImpl) BatchInsertChannels(channels []Channel) error {
	if len(channels) == 0 {
		return nil
	}

	ctx := context.Background()

	// 为每个channel生成ID并设置默认值
	for i := range channels {
		if channels[i].Id == 0 {
			nextId, err := m.getNextSequenceValue("channel_id")
			if err != nil {
				return err
			}
			channels[i].Id = nextId
		}

		// 设置默认值（MongoDB 不支持 schema 级别的默认值）
		// 根据 Channel 结构体中的 GORM default 标签设置默认值
		if channels[i].Status == 0 {
			channels[i].Status = 1 // default:1
		}
		if channels[i].ChannelGroupId == 0 {
			channels[i].ChannelGroupId = 0 // default:0 (已经是默认值)
		}
		if channels[i].Type == 0 {
			channels[i].Type = 0 // default:0 (已经是默认值)
		}
		if channels[i].Weight == nil {
			defaultWeight := uint(0) // default:0
			channels[i].Weight = &defaultWeight
		}
		if channels[i].BaseURL == nil {
			defaultBaseURL := "" // default:''
			channels[i].BaseURL = &defaultBaseURL
		}
		if channels[i].Group == "" {
			channels[i].Group = "default" // default:'default'
		}
		if channels[i].UsedQuota == 0 {
			channels[i].UsedQuota = 0 // default:0 (已经是默认值)
		}
		if channels[i].Sort == nil {
			defaultSort := 0 // default:0
			channels[i].Sort = &defaultSort
		}
		if channels[i].OverFrequencyAutoDisable == nil {
			defaultOverFrequency := false // default:0
			channels[i].OverFrequencyAutoDisable = &defaultOverFrequency
		}
		if channels[i].RetryInterval == nil {
			defaultRetryInterval := 300 // default:300
			channels[i].RetryInterval = &defaultRetryInterval
		}
		if channels[i].UndeadModeEnabled == nil {
			defaultUndeadMode := false // default:0
			channels[i].UndeadModeEnabled = &defaultUndeadMode
		}
		if channels[i].Priority == nil {
			defaultPriority := int64(0) // default:0
			channels[i].Priority = &defaultPriority
		}
		if channels[i].NonStrictTestMode == nil {
			defaultNonStrictTest := false // default:0
			channels[i].NonStrictTestMode = &defaultNonStrictTest
		}
		if channels[i].BillingType == 0 {
			channels[i].BillingType = 1 // default:1
		}
		if channels[i].FunctionCallEnabled == nil {
			defaultFunctionCall := true // default:1
			channels[i].FunctionCallEnabled = &defaultFunctionCall
		}
		if channels[i].ImageSupported == nil {
			defaultImageSupported := true // default:1
			channels[i].ImageSupported = &defaultImageSupported
		}
		if channels[i].ImageInMarkdown == nil {
			defaultImageInMarkdown := false // default:0
			channels[i].ImageInMarkdown = &defaultImageInMarkdown
		}
	}

	// 使用事务来同时插入渠道和创建abilities
	session, err := m.client.StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		// 转换为interface{}切片
		var docs []interface{}
		for _, channel := range channels {
			docs = append(docs, channel)
		}

		// 批量插入渠道
		_, err := m.channels.InsertMany(sc, docs)
		if err != nil {
			return err
		}

		// 为每个渠道创建abilities
		var allAbilities []Ability
		for _, channel := range channels {
			if channel.Models == "" || channel.Group == "" {
				continue // 如果没有模型或组，则跳过
			}

			models_ := strings.Split(channel.Models, ",")
			groups_ := strings.Split(channel.Group, ",")

			for _, model := range models_ {
				for _, group := range groups_ {
					// 清理字符串
					model = strings.TrimSpace(model)
					group = strings.TrimSpace(group)
					if model == "" || group == "" {
						continue
					}

					ability := Ability{
						Group:               group,
						Model:               model,
						ChannelId:           channel.Id,
						Enabled:             &[]bool{channel.Status == ChannelStatusEnabled}[0],
						Sort:                channel.Sort,
						Weight:              channel.Weight,
						Priority:            channel.Priority,
						BillingType:         channel.BillingType,
						FunctionCallEnabled: channel.FunctionCallEnabled,
						ImageSupported:      channel.ImageSupported,
					}
					allAbilities = append(allAbilities, ability)
				}
			}
		}

		// 批量插入所有abilities
		if len(allAbilities) > 0 {
			return m.BatchInsertAbilities(allAbilities)
		}

		return nil
	})

	return err
}

func (m *MongoDatabaseImpl) UpdateChannelBalance(id int, balance float64) error {
	ctx := context.Background()
	filter := bson.M{"id": id}
	update := bson.M{"$set": bson.M{"balance": balance}}
	_, err := m.channels.UpdateOne(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) UpdateChannelResponseTime(id int, responseTime int64) error {
	ctx := context.Background()
	filter := bson.M{"id": id}
	update := bson.M{"$set": bson.M{"response_time": responseTime}}
	_, err := m.channels.UpdateOne(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) DeleteChannelByStatus(status int64) (int64, error) {
	ctx := context.Background()
	filter := bson.M{"status": status}
	result, err := m.channels.DeleteMany(ctx, filter)
	if err != nil {
		return 0, err
	}
	return result.DeletedCount, nil
}

func (m *MongoDatabaseImpl) DeleteChannelByType(deleteType int) (int64, error) {
	ctx := context.Background()

	// 根据 deleteType 设置状态条件
	var statuses []int
	switch deleteType {
	case 2:
		statuses = append(statuses, 2) // common.ChannelStatusManuallyDisabled
	case 3:
		statuses = append(statuses, 3) // common.ChannelStatusAutoDisabled
	case 4:
		statuses = append(statuses, 4) // common.ChannelStatusMaxRetriesExceeded
	case 99: // 特例，如果需要删除所有已禁用的渠道
		statuses = append(statuses, 2, 3, 4) // 所有禁用状态
	default:
		return 0, fmt.Errorf("invalid deleteType: %d", deleteType)
	}

	// 构建查询条件
	filter := bson.M{"status": bson.M{"$in": statuses}}

	// 先查找符合条件的渠道ID，用于删除相关的abilities
	cursor, err := m.channels.Find(ctx, filter, options.Find().SetProjection(bson.M{"id": 1}))
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var channelIds []int
	for cursor.Next(ctx) {
		var result struct {
			ID int `bson:"id"`
		}
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		channelIds = append(channelIds, result.ID)
	}

	if len(channelIds) == 0 {
		return 0, nil
	}

	// 使用事务删除渠道和相关的abilities
	session, err := m.client.StartSession()
	if err != nil {
		return 0, err
	}
	defer session.EndSession(ctx)

	var deletedCount int64
	err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		// 删除渠道
		result, err := m.channels.DeleteMany(sc, filter)
		if err != nil {
			return err
		}
		deletedCount = result.DeletedCount

		// 删除相关的abilities
		_, err = m.abilities.DeleteMany(sc, bson.M{"channel_id": bson.M{"$in": channelIds}})
		return err
	})

	return deletedCount, err
}

func (m *MongoDatabaseImpl) DeleteChannelByDisableReason(disableReason string) (int64, error) {
	ctx := context.Background()

	// 根据不同的禁用原因构建不同的查询条件
	var filter bson.M
	switch disableReason {
	case "account_deactivated":
		filter = bson.M{"disableReason": bson.M{"$regex": "account.*deactivated", "$options": "i"}}
	case "quota_exceeded":
		filter = bson.M{"$or": []bson.M{
			{"disableReason": bson.M{"$regex": "quota.*exceeded", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "insufficient.*quota", "$options": "i"}},
		}}
	case "rate_limit_exceeded":
		filter = bson.M{"$or": []bson.M{
			{"disableReason": bson.M{"$regex": "rate.*limit", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "too.*many.*requests", "$options": "i"}},
		}}
	case "invalid_key":
		filter = bson.M{"$or": []bson.M{
			{"disableReason": bson.M{"$regex": "invalid.*key", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "incorrect.*api.*key", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "authentication.*failed", "$options": "i"}},
		}}
	case "connection_error":
		filter = bson.M{"$or": []bson.M{
			{"disableReason": bson.M{"$regex": "connection.*failed", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "timeout", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "refused", "$options": "i"}},
		}}
	case "internal_server_error":
		filter = bson.M{"$or": []bson.M{
			{"disableReason": bson.M{"$regex": "internal.*server.*error", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "500", "$options": "i"}},
			{"disableReason": bson.M{"$regex": "server.*error", "$options": "i"}},
		}}
	default:
		return 0, fmt.Errorf("invalid disableReason: %s", disableReason)
	}

	// 先查找符合条件的渠道ID，用于删除相关的abilities
	cursor, err := m.channels.Find(ctx, filter, options.Find().SetProjection(bson.M{"id": 1}))
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var channelIds []int
	for cursor.Next(ctx) {
		var result struct {
			ID int `bson:"id"`
		}
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
		channelIds = append(channelIds, result.ID)
	}

	if len(channelIds) == 0 {
		return 0, nil
	}

	// 使用事务删除渠道和相关的abilities
	session, err := m.client.StartSession()
	if err != nil {
		return 0, err
	}
	defer session.EndSession(ctx)

	var deletedCount int64
	err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		// 删除渠道
		result, err := m.channels.DeleteMany(sc, filter)
		if err != nil {
			return err
		}
		deletedCount = result.DeletedCount

		// 删除相关的abilities
		_, err = m.abilities.DeleteMany(sc, bson.M{"channel_id": bson.M{"$in": channelIds}})
		return err
	})

	return deletedCount, err
}

func (m *MongoDatabaseImpl) DeleteChannelByIds(ids []int) (int64, error) {
	ctx := context.Background()

	if len(ids) == 0 {
		return 0, nil
	}

	filter := bson.M{"id": bson.M{"$in": ids}}

	// 使用事务删除渠道和相关的abilities
	session, err := m.client.StartSession()
	if err != nil {
		return 0, err
	}
	defer session.EndSession(ctx)

	var deletedCount int64
	err = mongo.WithSession(ctx, session, func(sc mongo.SessionContext) error {
		// 删除渠道
		result, err := m.channels.DeleteMany(sc, filter)
		if err != nil {
			return err
		}
		deletedCount = result.DeletedCount

		// 删除相关的abilities
		_, err = m.abilities.DeleteMany(sc, bson.M{"channel_id": bson.M{"$in": ids}})
		return err
	})

	return deletedCount, err
}

func (m *MongoDatabaseImpl) CountChannelByStatus() (map[int]int, error) {
	ctx := context.Background()

	// 使用聚合管道进行分组统计
	pipeline := []bson.M{
		{"$group": bson.M{
			"_id":   "$status",
			"count": bson.M{"$sum": 1},
		}},
	}

	cursor, err := m.channels.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	countMap := make(map[int]int)
	for cursor.Next(ctx) {
		var result struct {
			ID    int `bson:"_id"`
			Count int `bson:"count"`
		}
		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}
		countMap[result.ID] = result.Count
	}

	return countMap, cursor.Err()
}

func (m *MongoDatabaseImpl) GetChannelModelMappingsBySudoGroup(group string) ([]Channel, error) {
	ctx := context.Background()

	filter := bson.M{
		"model_mapping": bson.M{
			"$exists": true,
			"$nin":    []string{"", "{}"},
		},
		"group": bson.M{"$regex": group, "$options": "i"},
	}

	opts := options.Find()
	opts.SetProjection(bson.M{
		"_id":           1,
		"name":          1,
		"group":         1,
		"model_mapping": 1,
		"key":           0, // 不返回key字段
	})

	cursor, err := m.channels.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var channels []Channel
	for cursor.Next(ctx) {
		var channel Channel
		if err := cursor.Decode(&channel); err != nil {
			return nil, err
		}
		channels = append(channels, channel)
	}

	return channels, cursor.Err()
}

func (m *MongoDatabaseImpl) GetAllModels() ([]string, error) {
	return GetAllModels()
}

func (m *MongoDatabaseImpl) GetChannelBanStatistics(disableReason string) ([]*BanStatistics, error) {
	ctx := context.Background()

	// 构建聚合管道
	pipeline := []bson.M{}

	// 添加分组阶段 - 直接使用remark字段分组
	groupStage := bson.M{
		"$group": bson.M{
			"_id": bson.M{
				"$ifNull": []interface{}{"$remark", "Unknown"},
			},
			"totalCount": bson.M{"$sum": 1},
			"bannedCount": bson.M{
				"$sum": bson.M{
					"$cond": []interface{}{
						bson.M{"$and": []interface{}{
							bson.M{"$eq": []interface{}{"$status", 3}},
							getBanCondition(disableReason),
						}},
						1,
						0,
					},
				},
			},
			"latestActiveCreatedTime": bson.M{
				"$max": bson.M{
					"$cond": []interface{}{
						bson.M{"$eq": []interface{}{"$status", 1}},
						"$created_time",
						nil,
					},
				},
			},
		},
	}

	// 添加计算阶段
	addFieldsStage := bson.M{
		"$addFields": bson.M{
			"activeCount": bson.M{"$subtract": []interface{}{"$totalCount", "$bannedCount"}},
			"banRate": bson.M{
				"$cond": []interface{}{
					bson.M{"$gt": []interface{}{"$totalCount", 0}},
					bson.M{"$multiply": []interface{}{
						bson.M{"$divide": []interface{}{"$bannedCount", "$totalCount"}},
						100,
					}},
					0,
				},
			},
		},
	}

	// 添加排序阶段
	sortStage := bson.M{
		"$sort": bson.M{
			"bannedCount": -1,
			"totalCount":  -1,
		},
	}

	// 构建完整的聚合管道
	pipeline = append(pipeline, groupStage, addFieldsStage, sortStage)

	// 执行聚合查询
	cursor, err := m.channels.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 处理结果
	var results []*BanStatistics
	for cursor.Next(ctx) {
		var result struct {
			ID                      interface{} `bson:"_id"`
			TotalCount              int64       `bson:"totalCount"`
			BannedCount             int64       `bson:"bannedCount"`
			ActiveCount             int64       `bson:"activeCount"`
			BanRate                 float64     `bson:"banRate"`
			LatestActiveCreatedTime *int64      `bson:"latestActiveCreatedTime"`
		}

		err := cursor.Decode(&result)
		if err != nil {
			return nil, err
		}

		groupName := "Unknown"
		if result.ID != nil {
			if str, ok := result.ID.(string); ok {
				groupName = str
			}
		}

		stat := &BanStatistics{
			GroupName:               groupName,
			TotalCount:              result.TotalCount,
			BannedCount:             result.BannedCount,
			ActiveCount:             result.ActiveCount,
			BanRate:                 result.BanRate,
			LatestActiveCreatedTime: result.LatestActiveCreatedTime,
		}

		results = append(results, stat)
	}

	return results, nil
}

// getBanCondition 根据disableReason构建MongoDB查询条件
func getBanCondition(disableReason string) bson.M {
	if disableReason == "all" {
		return bson.M{"$literal": true}
	}

	switch disableReason {
	case "account_deactivated":
		return bson.M{"$regexMatch": bson.M{"input": "$disableReason", "regex": "account.*deactivated", "options": "i"}}
	case "quota_exceeded":
		return bson.M{"$or": []bson.M{
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "quota.*exceeded", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "insufficient.*quota", "options": "i"}},
		}}
	case "rate_limit_exceeded":
		return bson.M{"$or": []bson.M{
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "rate.*limit", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "too.*many.*requests", "options": "i"}},
		}}
	case "invalid_key":
		return bson.M{"$or": []bson.M{
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "invalid.*key", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "authentication.*failed", "options": "i"}},
		}}
	case "connection_error":
		return bson.M{"$or": []bson.M{
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "connection.*failed", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "timeout", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "refused", "options": "i"}},
		}}
	case "internal_server_error":
		return bson.M{"$or": []bson.M{
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "internal.*server.*error", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "500", "options": "i"}},
			{"$regexMatch": bson.M{"input": "$disableReason", "regex": "server.*error", "options": "i"}},
		}}
	default:
		return bson.M{"$literal": false}
	}
}

// Ability operations - 占位符
func (m *MongoDatabaseImpl) GetAllAbilities(startIdx int, num int, orderBy string, group string, model string, channelId int, enabled bool) ([]*Ability, error) {
	ctx := context.Background()
	filter := bson.M{}

	if group != "" {
		groups := strings.Split(group, ",")
		filter["group"] = bson.M{"$in": groups}
	}
	if model != "" {
		filter["model"] = model
	}
	if channelId != 0 {
		filter["channel_id"] = channelId
	}
	if enabled {
		filter["enabled"] = enabled
	}

	opts := options.Find()
	if num > 0 {
		opts.SetSkip(int64(startIdx))
		opts.SetLimit(int64(num))
	}
	if orderBy != "" {
		opts.SetSort(bson.D{bson.E{Key: orderBy, Value: 1}})
	} else {
		opts.SetSort(bson.D{
			bson.E{Key: "group", Value: 1},
			bson.E{Key: "model", Value: 1},
			bson.E{Key: "channel_id", Value: 1},
		})
	}

	cursor, err := m.abilities.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var abilities []*Ability
	for cursor.Next(ctx) {
		var ability Ability
		if err := cursor.Decode(&ability); err != nil {
			return nil, err
		}
		abilities = append(abilities, &ability)
	}

	return abilities, cursor.Err()
}

func (m *MongoDatabaseImpl) CountAbilities(group string, model string, channelId int, enabled bool) (int64, error) {
	ctx := context.Background()
	filter := bson.M{}

	if group != "" {
		groups := strings.Split(group, ",")
		filter["group"] = bson.M{"$in": groups}
	}
	if model != "" {
		filter["model"] = model
	}
	if channelId != 0 {
		filter["channel_id"] = channelId
	}
	if enabled {
		filter["enabled"] = enabled
	}

	count, err := m.abilities.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (m *MongoDatabaseImpl) InsertAbility(ability *Ability) error {
	ctx := context.Background()

	// 设置默认值（MongoDB 不支持 schema 级别的默认值）
	// 根据 Ability 结构体中的 GORM default 标签设置默认值
	if ability.Enabled == nil {
		defaultEnabled := true // default:1
		ability.Enabled = &defaultEnabled
	}
	if ability.Sort == nil {
		defaultSort := 0 // default:0
		ability.Sort = &defaultSort
	}
	if ability.Weight == nil {
		defaultWeight := uint(0) // default:0
		ability.Weight = &defaultWeight
	}
	if ability.Priority == nil {
		defaultPriority := int64(0) // default:0
		ability.Priority = &defaultPriority
	}
	if ability.BillingType == 0 {
		ability.BillingType = 1 // default:1
	}
	if ability.FunctionCallEnabled == nil {
		defaultFunctionCall := true // default:1
		ability.FunctionCallEnabled = &defaultFunctionCall
	}
	if ability.ImageSupported == nil {
		defaultImageSupported := true // default:1
		ability.ImageSupported = &defaultImageSupported
	}

	_, err := m.abilities.InsertOne(ctx, ability)
	return err
}

// BatchInsertAbilities 批量插入abilities
func (m *MongoDatabaseImpl) BatchInsertAbilities(abilities []Ability) error {
	if len(abilities) == 0 {
		return nil
	}

	ctx := context.Background()

	// 为每个ability设置默认值
	for i := range abilities {
		// 设置默认值（MongoDB 不支持 schema 级别的默认值）
		// 根据 Ability 结构体中的 GORM default 标签设置默认值
		if abilities[i].Enabled == nil {
			defaultEnabled := true // default:1
			abilities[i].Enabled = &defaultEnabled
		}
		if abilities[i].Sort == nil {
			defaultSort := 0 // default:0
			abilities[i].Sort = &defaultSort
		}
		if abilities[i].Weight == nil {
			defaultWeight := uint(0) // default:0
			abilities[i].Weight = &defaultWeight
		}
		if abilities[i].Priority == nil {
			defaultPriority := int64(0) // default:0
			abilities[i].Priority = &defaultPriority
		}
		if abilities[i].BillingType == 0 {
			abilities[i].BillingType = 1 // default:1
		}
		if abilities[i].FunctionCallEnabled == nil {
			defaultFunctionCall := true // default:1
			abilities[i].FunctionCallEnabled = &defaultFunctionCall
		}
		if abilities[i].ImageSupported == nil {
			defaultImageSupported := true // default:1
			abilities[i].ImageSupported = &defaultImageSupported
		}
	}

	// 转换为interface{}切片
	var docs []interface{}
	for _, ability := range abilities {
		docs = append(docs, ability)
	}

	// 批量插入
	_, err := m.abilities.InsertMany(ctx, docs)
	return err
}

// createAbilitiesForChannel 为渠道创建abilities
func (m *MongoDatabaseImpl) createAbilitiesForChannel(channel *Channel) error {
	if channel.Models == "" || channel.Group == "" {
		return nil // 如果没有模型或组，则不创建abilities
	}

	models_ := strings.Split(channel.Models, ",")
	groups_ := strings.Split(channel.Group, ",")
	abilities := make([]Ability, 0, len(models_)*len(groups_))

	for _, model := range models_ {
		for _, group := range groups_ {
			// 清理字符串
			model = strings.TrimSpace(model)
			group = strings.TrimSpace(group)
			if model == "" || group == "" {
				continue
			}

			ability := Ability{
				Group:               group,
				Model:               model,
				ChannelId:           channel.Id,
				Enabled:             &[]bool{channel.Status == ChannelStatusEnabled}[0],
				Sort:                channel.Sort,
				Weight:              channel.Weight,
				Priority:            channel.Priority,
				BillingType:         channel.BillingType,
				FunctionCallEnabled: channel.FunctionCallEnabled,
				ImageSupported:      channel.ImageSupported,
			}
			abilities = append(abilities, ability)
		}
	}

	// 如果没有有效的abilities，直接返回
	if len(abilities) == 0 {
		return nil
	}

	// 批量插入abilities
	return m.BatchInsertAbilities(abilities)
}

func (m *MongoDatabaseImpl) UpdateAbility(ability *Ability) error {
	ctx := context.Background()
	filter := bson.M{
		"group":      ability.Group,
		"model":      ability.Model,
		"channel_id": ability.ChannelId,
	}

	// 构建更新文档，只包含非零值字段
	updateFields := bson.M{}

	if ability.Group != "" {
		updateFields["group"] = ability.Group
	}
	if ability.Model != "" {
		updateFields["model"] = ability.Model
	}
	if ability.ChannelId != 0 {
		updateFields["channel_id"] = ability.ChannelId
	}
	if ability.Enabled != nil {
		updateFields["enabled"] = ability.Enabled
	}
	if ability.Sort != nil {
		updateFields["sort"] = ability.Sort
	}
	if ability.Weight != nil {
		updateFields["weight"] = ability.Weight
	}
	if ability.Priority != nil {
		updateFields["priority"] = ability.Priority
	}
	if ability.BillingType != 0 {
		updateFields["billing_type"] = ability.BillingType
	}
	if ability.FunctionCallEnabled != nil {
		updateFields["function_call_enabled"] = ability.FunctionCallEnabled
	}
	if ability.ImageSupported != nil {
		updateFields["image_supported"] = ability.ImageSupported
	}

	// 如果没有字段需要更新，直接返回
	if len(updateFields) == 0 {
		return nil
	}

	update := bson.M{"$set": updateFields}
	_, err := m.abilities.UpdateOne(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) DeleteAbility(ability *Ability) error {
	ctx := context.Background()
	filter := bson.M{
		"group":      ability.Group,
		"model":      ability.Model,
		"channel_id": ability.ChannelId,
	}
	_, err := m.abilities.DeleteOne(ctx, filter)
	return err
}

func (m *MongoDatabaseImpl) DeleteAbilitiesByIds(ids []int) (int64, error) {
	ctx := context.Background()
	filter := bson.M{"channel_id": bson.M{"$in": ids}}
	result, err := m.abilities.DeleteMany(ctx, filter)
	if err != nil {
		return 0, err
	}
	return result.DeletedCount, nil
}

func (m *MongoDatabaseImpl) GetAbilityByGroupModelChannel(group string, model string, channelId int) (*Ability, error) {
	ctx := context.Background()
	filter := bson.M{
		"group":      group,
		"model":      model,
		"channel_id": channelId,
	}
	var ability Ability
	err := m.abilities.FindOne(ctx, filter).Decode(&ability)
	if err != nil {
		return nil, err
	}
	return &ability, nil
}

func (m *MongoDatabaseImpl) UpdateAbilityEnabledStatus(group string, model string, channelId int, enabled bool) error {
	ctx := context.Background()
	filter := bson.M{
		"group":      group,
		"model":      model,
		"channel_id": channelId,
	}
	update := bson.M{"$set": bson.M{"enabled": enabled}}
	_, err := m.abilities.UpdateOne(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) GetAbilitiesByChannelId(channelId int) ([]*Ability, error) {
	ctx := context.Background()
	filter := bson.M{"channel_id": channelId}
	cursor, err := m.abilities.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var abilities []*Ability
	for cursor.Next(ctx) {
		var ability Ability
		if err := cursor.Decode(&ability); err != nil {
			return nil, err
		}
		abilities = append(abilities, &ability)
	}

	return abilities, cursor.Err()
}

func (m *MongoDatabaseImpl) GetRandomSatisfiedChannel(group string, model string, tokenBillingType int, inputHasFunctionCall bool, inputHasImage bool, excludeIds []int, ignoreFirstPriority bool, isV1MessagesPath bool) (*Channel, error) {
	ctx := context.Background()
	filter := bson.M{
		"group":   group,
		"model":   bson.M{"$regex": "^" + strings.ReplaceAll(model, "*", ".*") + "$"},
		"enabled": true,
	}

	if tokenBillingType != common.BillingTypeMixed {
		filter["billing_type"] = tokenBillingType
	}
	if inputHasFunctionCall {
		filter["function_call_enabled"] = true
	}
	if inputHasImage {
		filter["image_supported"] = true
	}
	if len(excludeIds) > 0 {
		filter["channel_id"] = bson.M{"$nin": excludeIds}
	}

	// 获取所有符合条件的能力
	cursor, err := m.abilities.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var abilities []*Ability
	for cursor.Next(ctx) {
		var ability Ability
		if err := cursor.Decode(&ability); err != nil {
			return nil, err
		}
		abilities = append(abilities, &ability)
	}

	if len(abilities) == 0 {
		return nil, errors.New("no available channel")
	}

	// 如果启用了全局忽略优先级，直接随机选择一个渠道能力
	if config.GlobalIgnorePriorityEnabled {
		randomAbility := abilities[rand.Intn(len(abilities))]
		var channel Channel
		err := m.channels.FindOne(ctx, bson.M{"id": randomAbility.ChannelId}).Decode(&channel)
		return &channel, err
	}

	// 如果启用了全局忽略权重计算，直接随机选择一个渠道能力
	if config.GlobalIgnoreWeightCalculationEnabled {
		randomAbility := abilities[rand.Intn(len(abilities))]
		var channel Channel
		err := m.channels.FindOne(ctx, bson.M{"id": randomAbility.ChannelId}).Decode(&channel)
		return &channel, err
	}

	// 平滑系数，避免权重为0的渠道完全没有机会被选中
	smoothingFactor := 10

	// 计算总权重
	totalWeight := 0
	for _, ab := range abilities {
		totalWeight += int(ab.GetWeight()) + smoothingFactor
	}

	// 生成随机值
	randomWeight := rand.Intn(totalWeight)

	// 根据权重选择渠道
	var selectedAbility *Ability
	for _, ab := range abilities {
		randomWeight -= int(ab.GetWeight()) + smoothingFactor
		if randomWeight <= 0 {
			selectedAbility = ab
			break
		}
	}

	// 兜底：如果没有选中任何渠道，随机选择一个
	if selectedAbility == nil && len(abilities) > 0 {
		selectedAbility = abilities[rand.Intn(len(abilities))]
	}

	var channel Channel
	err = m.channels.FindOne(ctx, bson.M{"id": selectedAbility.ChannelId}).Decode(&channel)
	return &channel, err
}

func (m *MongoDatabaseImpl) UpdateAbilityStatus(channelId int, status bool) error {
	ctx := context.Background()
	filter := bson.M{"channel_id": channelId}
	update := bson.M{"$set": bson.M{"enabled": status}}
	_, err := m.abilities.UpdateMany(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) GetGroupModels(ctx context.Context, group string) ([]string, error) {
	filter := bson.M{
		"group":   group,
		"enabled": true,
	}
	opts := options.Distinct().SetMaxTime(10 * time.Second)
	models, err := m.abilities.Distinct(ctx, "model", filter, opts)
	if err != nil {
		return nil, err
	}

	var result []string
	for _, model := range models {
		if str, ok := model.(string); ok {
			result = append(result, str)
		}
	}
	sort.Strings(result)
	return result, nil
}

func (m *MongoDatabaseImpl) GetAvailableModelByGroup(group string) []string {
	ctx := context.Background()
	filter := bson.M{
		"group":   group,
		"enabled": true,
	}
	opts := options.Distinct().SetMaxTime(10 * time.Second)
	models, err := m.abilities.Distinct(ctx, "model", filter, opts)
	if err != nil {
		return nil
	}

	var result []string
	for _, model := range models {
		if str, ok := model.(string); ok {
			result = append(result, str)
		}
	}
	return result
}

func (m *MongoDatabaseImpl) HasSearchSerperModel() bool {
	ctx := context.Background()
	filter := bson.M{
		"model":   "search-serper",
		"enabled": true,
	}
	count, err := m.abilities.CountDocuments(ctx, filter)
	if err != nil {
		return false
	}
	return count > 0
}

func (m *MongoDatabaseImpl) DeleteAbilitiesNotExistsInChannel() (int64, error) {
	ctx := context.Background()

	// 尝试使用事务，如果失败则使用普通操作
	session, err := m.client.StartSession()
	if err != nil {
		return m.deleteAbilitiesNotExistsInChannelWithoutTransaction(ctx)
	}
	defer session.EndSession(ctx)

	var totalDeleted int64
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 获取所有channel_id
		var channelIds []int
		cursor, err := m.channels.Find(sessCtx, bson.M{}, options.Find().SetProjection(bson.M{"id": 1}))
		if err != nil {
			return nil, err
		}
		defer cursor.Close(sessCtx)

		for cursor.Next(sessCtx) {
			var channel struct {
				Id int `bson:"id"`
			}
			if err := cursor.Decode(&channel); err != nil {
				return nil, err
			}
			channelIds = append(channelIds, channel.Id)
		}

		// 删除不在channel_ids中的abilities
		filter := bson.M{"channel_id": bson.M{"$nin": channelIds}}
		result, err := m.abilities.DeleteMany(sessCtx, filter)
		if err != nil {
			return nil, err
		}
		totalDeleted += result.DeletedCount

		// 更新enabled!=true的字段为false
		filter = bson.M{"enabled": bson.M{"$ne": true}}
		update := bson.M{"$set": bson.M{"enabled": false}}
		updateResult, err := m.abilities.UpdateMany(sessCtx, filter, update)
		if err != nil {
			return nil, err
		}
		totalDeleted += updateResult.ModifiedCount

		return nil, nil
	})

	if err != nil {
		// 如果事务失败（可能是因为单机MongoDB不支持事务），使用普通操作
		if isTransactionNotSupportedError(err) {
			return m.deleteAbilitiesNotExistsInChannelWithoutTransaction(ctx)
		}
		return 0, err
	}

	return totalDeleted, nil
}

// deleteAbilitiesNotExistsInChannelWithoutTransaction 不使用事务的删除操作
func (m *MongoDatabaseImpl) deleteAbilitiesNotExistsInChannelWithoutTransaction(ctx context.Context) (int64, error) {
	var totalDeleted int64

	// 获取所有channel_id
	var channelIds []int
	cursor, err := m.channels.Find(ctx, bson.M{}, options.Find().SetProjection(bson.M{"id": 1}))
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var channel struct {
			Id int `bson:"id"`
		}
		if err := cursor.Decode(&channel); err != nil {
			return 0, err
		}
		channelIds = append(channelIds, channel.Id)
	}

	// 删除不在channel_ids中的abilities
	filter := bson.M{"channel_id": bson.M{"$nin": channelIds}}
	result, err := m.abilities.DeleteMany(ctx, filter)
	if err != nil {
		return 0, err
	}
	totalDeleted += result.DeletedCount

	// 更新enabled!=true的字段为false
	filter = bson.M{"enabled": bson.M{"$ne": true}}
	update := bson.M{"$set": bson.M{"enabled": false}}
	updateResult, err := m.abilities.UpdateMany(ctx, filter, update)
	if err != nil {
		return totalDeleted, err
	}
	totalDeleted += updateResult.ModifiedCount

	return totalDeleted, nil
}

func (m *MongoDatabaseImpl) GetFirstAbilityByChannelIdAndModel(channelId int, model string) (*Ability, error) {
	ctx := context.Background()
	filter := bson.M{
		"channel_id": channelId,
		"model":      model,
	}
	var ability Ability
	err := m.abilities.FindOne(ctx, filter).Decode(&ability)
	if err != nil {
		return nil, err
	}
	return &ability, nil
}

func (m *MongoDatabaseImpl) GetAllDistinctModels() ([]string, error) {
	ctx := context.Background()
	models, err := m.abilities.Distinct(ctx, "model", bson.M{})
	if err != nil {
		return nil, err
	}

	var result []string
	for _, model := range models {
		if str, ok := model.(string); ok {
			result = append(result, str)
		}
	}
	sort.Strings(result)
	return result, nil
}

func (m *MongoDatabaseImpl) GetAllEnabledDistinctModels() ([]string, error) {
	ctx := context.Background()
	filter := bson.M{"enabled": true}
	models, err := m.abilities.Distinct(ctx, "model", filter)
	if err != nil {
		return nil, err
	}

	var result []string
	for _, model := range models {
		if str, ok := model.(string); ok {
			result = append(result, str)
		}
	}
	sort.Strings(result)
	return result, nil
}

// ChannelExtend operations - 占位符
func (m *MongoDatabaseImpl) GetChannelExtendByChannelId(channelId int) (*ChannelExtend, error) {
	ctx := context.Background()
	filter := bson.M{"channel_id": channelId}

	var channelExtend ChannelExtend
	err := m.channelExs.FindOne(ctx, filter).Decode(&channelExtend)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("channel extend not found")
		}
		return nil, err
	}

	return &channelExtend, nil
}

func (m *MongoDatabaseImpl) InsertChannelExtend(channelExtend *ChannelExtend) error {
	ctx := context.Background()

	// 生成自增ID
	if channelExtend.Id == 0 {
		// 获取下一个ID
		nextId, err := m.getNextSequenceValue("channel_extend_id")
		if err != nil {
			return err
		}
		channelExtend.Id = nextId
	}

	// 设置默认值（MongoDB 不支持 schema 级别的默认值）
	// 根据 channel-ex.go 中的 GORM default 标签设置默认值
	if channelExtend.FilterStreamAdMinSize == 0 {
		channelExtend.FilterStreamAdMinSize = 10 // default:10
	}
	// 其他布尔字段默认值都是 false（0），数值字段默认值都是 0，所以不需要特殊处理

	_, err := m.channelExs.InsertOne(ctx, channelExtend)
	return err
}

func (m *MongoDatabaseImpl) UpdateChannelExtend(channelExtend *ChannelExtend) error {
	ctx := context.Background()
	filter := bson.M{"channel_id": channelExtend.ChannelId}

	// 构建更新文档，只包含非零值字段
	updateFields := bson.M{}

	if channelExtend.Id != 0 {
		updateFields["id"] = channelExtend.Id
	}
	if channelExtend.ChannelId != 0 {
		updateFields["channel_id"] = channelExtend.ChannelId
	}
	if channelExtend.PlatformAccessToken != "" {
		updateFields["platform_access_token"] = channelExtend.PlatformAccessToken
	}
	if channelExtend.UpstreamUserId != "" {
		updateFields["upstream_user_id"] = channelExtend.UpstreamUserId
	}
	if channelExtend.ExtraHeaders != nil && *channelExtend.ExtraHeaders != "" {
		updateFields["extra_headers"] = channelExtend.ExtraHeaders
	}
	if channelExtend.FilterNonStreamAdRegex != "" {
		updateFields["filter_non_stream_ad_regex"] = channelExtend.FilterNonStreamAdRegex
	}
	if channelExtend.CustomSystemPrompt != "" {
		updateFields["custom_system_prompt"] = channelExtend.CustomSystemPrompt
	}
	if channelExtend.ParseUrlPrefix != "" {
		updateFields["parse_url_prefix"] = channelExtend.ParseUrlPrefix
	}
	if channelExtend.Base64ImagePrefixMapping != nil && *channelExtend.Base64ImagePrefixMapping != "" {
		updateFields["base64_image_prefix_mapping"] = channelExtend.Base64ImagePrefixMapping
	}
	if channelExtend.KeywordError != "" {
		updateFields["keyword_error"] = channelExtend.KeywordError
	}

	// 布尔字段 - 这些字段需要特殊处理，因为false也是有效值
	updateFields["filter_stream_ad"] = channelExtend.FilterStreamAd
	updateFields["filter_non_stream_ad"] = channelExtend.FilterNonStreamAd
	updateFields["filter_system_prompt"] = channelExtend.FilterSystemPrompt
	updateFields["parse_url_to_content"] = channelExtend.ParseUrlToContent
	updateFields["parse_url_prefix_enabled"] = channelExtend.ParseUrlPrefixEnabled
	updateFields["custom_full_url_enabled"] = channelExtend.CustomFullUrlEnabled
	updateFields["arrange_messages"] = channelExtend.ArrangeMessages
	updateFields["original_model_pricing"] = channelExtend.OriginalModelPricing
	updateFields["negative_optimization_enabled"] = channelExtend.NegativeOptimizationEnabled
	updateFields["original_model_fake_resp_enabled"] = channelExtend.OriginalModelFakeRespEnabled
	updateFields["fake_completion_id_enabled"] = channelExtend.FakeCompletionIdEnabled
	updateFields["exclude_custom_prompt_cost_enabled"] = channelExtend.ExcludeCustomPromptCostEnabled
	updateFields["force_chat_url_enabled"] = channelExtend.ForceChatUrlEnabled
	updateFields["ignore_fc_tc_enabled"] = channelExtend.IgnoreFcTcEnabled
	updateFields["usage_recalculation_enabled"] = channelExtend.UsageRecalculationEnabled
	updateFields["empty_response_error_enabled"] = channelExtend.EmptyResponseErrorEnabled
	updateFields["remove_image_download_error_enabled"] = channelExtend.RemoveImageDownloadErrorEnabled
	updateFields["request_token_limit_enabled"] = channelExtend.RequestTokenLimitEnabled
	updateFields["claude_stream_enabled"] = channelExtend.ClaudeStreamEnabled
	updateFields["keyword_error_enabled"] = channelExtend.KeywordErrorEnabled
	updateFields["transparent_proxy_enabled"] = channelExtend.TransparentProxyEnabled
	updateFields["force_o1_stream_enabled"] = channelExtend.ForceO1StreamEnabled
	updateFields["think_tag_processing_enabled"] = channelExtend.ThinkTagProcessingEnabled
	updateFields["image_chat_conversion_enabled"] = channelExtend.ImageChatConversionEnabled

	// 数值字段
	if channelExtend.FilterStreamAdMinSize != 0 {
		updateFields["filter_stream_ad_min_size"] = channelExtend.FilterStreamAdMinSize
	}
	if channelExtend.NegativeOptimizationTime != 0 {
		updateFields["negative_optimization_time"] = channelExtend.NegativeOptimizationTime
	}
	if channelExtend.NegativeRandomOffset != 0 {
		updateFields["negative_random_offset"] = channelExtend.NegativeRandomOffset
	}
	if channelExtend.ChannelTimeoutBreakerTime != 0 {
		updateFields["channel_timeout_breaker_time"] = channelExtend.ChannelTimeoutBreakerTime
	}
	if channelExtend.MinRequestTokenCount != 0 {
		updateFields["min_request_token_count"] = channelExtend.MinRequestTokenCount
	}
	if channelExtend.MaxRequestTokenCount != 0 {
		updateFields["max_request_token_count"] = channelExtend.MaxRequestTokenCount
	}
	if channelExtend.CostPerUnit != 0 {
		updateFields["cost_per_unit"] = channelExtend.CostPerUnit
	}
	if channelExtend.ImageChatConversionModels != "" {
		updateFields["image_chat_conversion_models"] = channelExtend.ImageChatConversionModels
	}

	update := bson.M{"$set": updateFields}
	_, err := m.channelExs.UpdateOne(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) DeleteChannelExtend(channelExtend *ChannelExtend) error {
	ctx := context.Background()
	filter := bson.M{"channel_id": channelExtend.ChannelId}
	_, err := m.channelExs.DeleteOne(ctx, filter)
	return err
}

func (m *MongoDatabaseImpl) DeleteChannelExtendByChannelId(channelId int) error {
	ctx := context.Background()
	filter := bson.M{"channel_id": channelId}
	_, err := m.channelExs.DeleteOne(ctx, filter)
	return err
}

func (m *MongoDatabaseImpl) BatchInsertChannelExtends(channelExtends []ChannelExtend, sqlOnly bool) error {
	if len(channelExtends) == 0 {
		return nil
	}

	ctx := context.Background()

	// 为每个channelExtend生成ID
	for i := range channelExtends {
		if channelExtends[i].Id == 0 {
			nextId, err := m.getNextSequenceValue("channel_extend_id")
			if err != nil {
				return err
			}
			channelExtends[i].Id = nextId
		}

		// 设置默认值（MongoDB 不支持 schema 级别的默认值）
		if channelExtends[i].FilterStreamAdMinSize == 0 {
			channelExtends[i].FilterStreamAdMinSize = 10 // default:10
		}
	}

	// 转换为interface{}切片
	var docs []interface{}
	for _, channelExtend := range channelExtends {
		docs = append(docs, channelExtend)
	}

	// 批量插入
	_, err := m.channelExs.InsertMany(ctx, docs)
	return err
}

// Batch operations - 占位符
func (m *MongoDatabaseImpl) BatchUpdateChannelsToChannelGroup(channelIds []int, channelGroupId int) error {
	ctx := context.Background()

	filter := bson.M{"id": bson.M{"$in": channelIds}}
	update := bson.M{"$set": bson.M{"channel_group_id": channelGroupId}}

	_, err := m.channels.UpdateMany(ctx, filter, update)
	return err
}

// ChannelGroup operations
func (m *MongoDatabaseImpl) GetChannelGroupById(id int, selectAll bool) (*ChannelGroup, error) {
	ctx := context.Background()
	filter := bson.M{"id": id}

	var projection bson.M
	if !selectAll {
		projection = bson.M{"key": 0}
	}

	opts := options.FindOne()
	if projection != nil {
		opts.SetProjection(projection)
	}

	var channelGroup ChannelGroup
	err := m.channelGroups.FindOne(ctx, filter, opts).Decode(&channelGroup)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("channel group not found")
		}
		return nil, err
	}

	return &channelGroup, nil
}

func (m *MongoDatabaseImpl) GetAllChannelGroups(startIdx int, num int, selectAll bool, id int, name string, status int, group string) ([]*ChannelGroup, error) {
	ctx := context.Background()
	filter := bson.M{}

	// 构建查询条件
	if id != 0 {
		filter["id"] = id
	}
	if name != "" {
		filter["name"] = bson.M{"$regex": name, "$options": "i"}
	}
	if status != 0 {
		filter["status"] = status
	}
	if group != "" {
		filter["group"] = bson.M{"$regex": group, "$options": "i"}
	}

	// 构建查询选项
	opts := options.Find()
	opts.SetSort(bson.D{{"sort", -1}, {"id", -1}})

	if !selectAll {
		opts.SetSkip(int64(startIdx))
		opts.SetLimit(int64(num))
	}

	// 执行查询
	cursor, err := m.channelGroups.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var channelGroups []*ChannelGroup
	for cursor.Next(ctx) {
		var channelGroup ChannelGroup
		if err := cursor.Decode(&channelGroup); err != nil {
			return nil, err
		}
		channelGroups = append(channelGroups, &channelGroup)
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	// 如果查询成功且有数据，为每个渠道组获取统计信息
	if len(channelGroups) > 0 {
		// 收集所有渠道组ID
		groupIds := make([]int, len(channelGroups))
		for i, group := range channelGroups {
			groupIds[i] = group.Id
		}

		// 一次性查询所有渠道组的统计信息
		statsMap, err := m.getChannelGroupsStatsMongo(groupIds)
		if err != nil {
			// 如果统计查询失败，记录错误但不影响主查询
			logger.SysError("failed to get channel group stats: " + err.Error())
		} else {
			// 为每个渠道组分配对应的统计信息
			for _, group := range channelGroups {
				if stats, exists := statsMap[group.Id]; exists {
					group.ChannelStats = stats
				} else {
					// 如果没有找到统计信息，设置默认值
					group.ChannelStats = &ChannelGroupStats{
						Total: 0, Enabled: 0, Disabled: 0, AutoDisabled: 0, MaxRetriesExceeded: 0, PartiallyDisabled: 0, Other: 0,
					}
				}
			}
		}
	}

	return channelGroups, nil
}

// getChannelGroupsStatsMongo MongoDB版本的批量获取渠道组统计信息
func (m *MongoDatabaseImpl) getChannelGroupsStatsMongo(groupIds []int) (map[int]*ChannelGroupStats, error) {
	if len(groupIds) == 0 {
		return make(map[int]*ChannelGroupStats), nil
	}

	ctx := context.Background()
	statsMap := make(map[int]*ChannelGroupStats)

	// 使用聚合管道获取统计信息
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"channel_group_id": bson.M{"$in": groupIds},
			},
		},
		{
			"$group": bson.M{
				"_id": bson.M{
					"channel_group_id": "$channel_group_id",
					"status":           "$status",
				},
				"count": bson.M{"$sum": 1},
			},
		},
	}

	cursor, err := m.channels.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 初始化所有渠道组的统计信息
	for _, groupId := range groupIds {
		statsMap[groupId] = &ChannelGroupStats{
			Total: 0, Enabled: 0, Disabled: 0, AutoDisabled: 0, MaxRetriesExceeded: 0, PartiallyDisabled: 0, Other: 0,
		}
	}

	// 处理聚合结果
	for cursor.Next(ctx) {
		var result struct {
			ID struct {
				ChannelGroupId int `bson:"channel_group_id"`
				Status         int `bson:"status"`
			} `bson:"_id"`
			Count int64 `bson:"count"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		stats := statsMap[result.ID.ChannelGroupId]
		stats.Total += result.Count

		switch result.ID.Status {
		case 1:
			stats.Enabled = result.Count
		case 2:
			stats.Disabled = result.Count
		case 3:
			stats.AutoDisabled = result.Count
		case 4:
			stats.MaxRetriesExceeded = result.Count
		case 5:
			stats.PartiallyDisabled = result.Count
		default:
			stats.Other += result.Count
		}
	}

	return statsMap, cursor.Err()
}

func (m *MongoDatabaseImpl) SearchChannelGroups(startIdx int, num int, keyword string) ([]*ChannelGroup, error) {
	ctx := context.Background()
	filter := bson.M{}

	// 尝试将keyword转换为整数
	if keywordInt, err := strconv.Atoi(keyword); err == nil {
		filter["$or"] = []bson.M{
			{"id": keywordInt},
			{"name": bson.M{"$regex": keyword, "$options": "i"}},
			{"key": keyword},
			{"group": bson.M{"$regex": keyword, "$options": "i"}},
		}
	} else {
		filter["$or"] = []bson.M{
			{"name": bson.M{"$regex": keyword, "$options": "i"}},
			{"key": keyword},
			{"group": bson.M{"$regex": keyword, "$options": "i"}},
		}
	}

	opts := options.Find()
	opts.SetSkip(int64(startIdx))
	opts.SetLimit(int64(num))
	opts.SetSort(bson.D{bson.E{Key: "sort", Value: -1}, bson.E{Key: "id", Value: -1}})
	opts.SetProjection(bson.M{"key": 0})

	cursor, err := m.channelGroups.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var channelGroups []*ChannelGroup
	for cursor.Next(ctx) {
		var channelGroup ChannelGroup
		if err := cursor.Decode(&channelGroup); err != nil {
			return nil, err
		}
		channelGroups = append(channelGroups, &channelGroup)
	}

	return channelGroups, cursor.Err()
}

func (m *MongoDatabaseImpl) CountChannelGroups(id int, name string, group string) (int64, error) {
	ctx := context.Background()
	filter := bson.M{}

	if id != 0 {
		filter["id"] = id
	}
	if name != "" {
		filter["name"] = bson.M{"$regex": name, "$options": "i"}
	}
	if group != "" {
		filter["group"] = bson.M{"$regex": group, "$options": "i"}
	}

	count, err := m.channelGroups.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (m *MongoDatabaseImpl) InsertChannelGroup(channelGroup *ChannelGroup) error {
	ctx := context.Background()

	// 生成自增ID
	if channelGroup.Id == 0 {
		// 获取下一个ID
		nextId, err := m.getNextSequenceValue("channel_group_id")
		if err != nil {
			return err
		}
		channelGroup.Id = nextId
	}

	// 设置默认值（MongoDB 不支持 schema 级别的默认值）
	// 根据 ChannelGroup 结构体中的 GORM default 标签设置默认值
	if channelGroup.Type == 0 {
		channelGroup.Type = 0 // default:0 (已经是默认值)
	}
	if channelGroup.Status == 0 {
		channelGroup.Status = 1 // default:1
	}
	if channelGroup.Weight == nil {
		defaultWeight := uint(0) // default:0
		channelGroup.Weight = &defaultWeight
	}
	if channelGroup.BaseURL == nil {
		defaultBaseURL := "" // default:''
		channelGroup.BaseURL = &defaultBaseURL
	}
	if channelGroup.Group == "" {
		channelGroup.Group = "default" // default:'default'
	}
	if channelGroup.Sort == nil {
		defaultSort := 0 // default:0
		channelGroup.Sort = &defaultSort
	}
	if channelGroup.OverFrequencyAutoDisable == nil {
		defaultOverFrequency := false // default:0
		channelGroup.OverFrequencyAutoDisable = &defaultOverFrequency
	}
	if channelGroup.RetryInterval == nil {
		defaultRetryInterval := 300 // default:300
		channelGroup.RetryInterval = &defaultRetryInterval
	}
	if channelGroup.RequestTokenLimitEnabled == nil {
		defaultRequestTokenLimit := false // default:0
		channelGroup.RequestTokenLimitEnabled = &defaultRequestTokenLimit
	}
	if channelGroup.MinRequestTokenCount == nil {
		defaultMinRequestToken := int64(0) // default:0
		channelGroup.MinRequestTokenCount = &defaultMinRequestToken
	}
	if channelGroup.MaxRequestTokenCount == nil {
		defaultMaxRequestToken := int64(0) // default:0
		channelGroup.MaxRequestTokenCount = &defaultMaxRequestToken
	}

	_, err := m.channelGroups.InsertOne(ctx, channelGroup)
	return err
}

func (m *MongoDatabaseImpl) UpdateChannelGroup(channelGroup *ChannelGroup) error {
	ctx := context.Background()

	// 尝试使用事务，如果失败则使用普通操作
	session, err := m.client.StartSession()
	if err != nil {
		return m.updateChannelGroupWithoutTransaction(ctx, channelGroup)
	}
	defer session.EndSession(ctx)

	// 使用事务确保数据一致性
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		return m.performChannelGroupUpdate(sessCtx, channelGroup)
	})

	if err != nil {
		// 如果事务失败（可能是因为单机MongoDB不支持事务），使用普通操作
		if isTransactionNotSupportedError(err) {
			return m.updateChannelGroupWithoutTransaction(ctx, channelGroup)
		}
		return err
	}
	return nil
}

// updateChannelGroupWithoutTransaction 不使用事务的渠道组更新
func (m *MongoDatabaseImpl) updateChannelGroupWithoutTransaction(ctx context.Context, channelGroup *ChannelGroup) error {
	_, err := m.performChannelGroupUpdate(ctx, channelGroup)
	return err
}

// performChannelGroupUpdate 执行渠道组更新的核心逻辑
func (m *MongoDatabaseImpl) performChannelGroupUpdate(ctx context.Context, channelGroup *ChannelGroup) (interface{}, error) {
	// 1. 更新渠道组基本信息
	filter := bson.M{"id": channelGroup.Id}

	// 构建更新文档，只包含非零值字段
	updateFields := bson.M{}
	if channelGroup.Type != 0 {
		updateFields["type"] = channelGroup.Type
	}
	if channelGroup.Status != 0 {
		updateFields["status"] = channelGroup.Status
	}
	if channelGroup.Name != "" {
		updateFields["name"] = channelGroup.Name
	}
	if channelGroup.Weight != nil {
		updateFields["weight"] = channelGroup.Weight
	}
	if channelGroup.CreatedTime != 0 {
		updateFields["created_time"] = channelGroup.CreatedTime
	}
	if channelGroup.BaseURL != nil && *channelGroup.BaseURL != "" {
		updateFields["base_url"] = channelGroup.BaseURL
	}
	if channelGroup.Models != "" {
		updateFields["models"] = channelGroup.Models
	}
	if channelGroup.Group != "" {
		updateFields["group"] = channelGroup.Group
	}
	if channelGroup.Sort != nil {
		updateFields["sort"] = channelGroup.Sort
	}
	if channelGroup.OverFrequencyAutoDisable != nil {
		updateFields["overFrequencyAutoDisable"] = channelGroup.OverFrequencyAutoDisable
	}
	if channelGroup.RetryInterval != nil {
		updateFields["retryInterval"] = channelGroup.RetryInterval
	}
	if channelGroup.RequestTokenLimitEnabled != nil {
		updateFields["request_token_limit_enabled"] = channelGroup.RequestTokenLimitEnabled
	}
	if channelGroup.MinRequestTokenCount != nil {
		updateFields["min_request_token_count"] = channelGroup.MinRequestTokenCount
	}
	if channelGroup.MaxRequestTokenCount != nil {
		updateFields["max_request_token_count"] = channelGroup.MaxRequestTokenCount
	}
	if channelGroup.Config != "" {
		updateFields["config"] = channelGroup.Config
	}

	update := bson.M{"$set": updateFields}
	_, err := m.channelGroups.UpdateOne(ctx, filter, update)
	if err != nil {
		return nil, err
	}

	// 2. 查找该渠道组下的所有渠道
	var channels []*Channel
	cursor, err := m.channels.Find(ctx, bson.M{"channel_group_id": channelGroup.Id})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if err = cursor.All(ctx, &channels); err != nil {
		return nil, err
	}

	if len(channels) == 0 {
		return nil, nil
	}

	// 3. 删除这些渠道的abilities
	channelIds := make([]int, len(channels))
	for i, channel := range channels {
		channelIds[i] = channel.Id
	}
	_, err = m.abilities.DeleteMany(ctx, bson.M{"channel_id": bson.M{"$in": channelIds}})
	if err != nil {
		return nil, err
	}

	// 4. 更新这些渠道的信息
	update = bson.M{
		"$set": bson.M{
			"status":                   channelGroup.Status,
			"base_url":                 channelGroup.BaseURL,
			"models":                   channelGroup.Models,
			"group":                    channelGroup.Group,
			"sort":                     channelGroup.Sort,
			"weight":                   channelGroup.Weight,
			"overFrequencyAutoDisable": channelGroup.OverFrequencyAutoDisable,
			"retryInterval":            channelGroup.RetryInterval,
			"config":                   channelGroup.Config,
		},
	}
	_, err = m.channels.UpdateMany(ctx, bson.M{"channel_group_id": channelGroup.Id}, update)
	if err != nil {
		return nil, err
	}

	// 5. 为每个渠道创建新的abilities
	for _, channel := range channels {
		models := strings.Split(channelGroup.Models, ",")
		groups := strings.Split(channelGroup.Group, ",")
		abilities := make([]interface{}, 0, len(models)*len(groups))

		for _, model := range models {
			for _, group := range groups {
				ability := Ability{
					Group:               group,
					Model:               model,
					ChannelId:           channel.Id,
					Enabled:             &[]bool{channelGroup.Status == common.ChannelStatusEnabled}[0],
					Sort:                channelGroup.Sort,
					Weight:              channelGroup.Weight,
					BillingType:         channel.BillingType,
					FunctionCallEnabled: channel.FunctionCallEnabled,
					ImageSupported:      channel.ImageSupported,
				}
				abilities = append(abilities, ability)
			}
		}

		if len(abilities) > 0 {
			_, err = m.abilities.InsertMany(ctx, abilities)
			if err != nil {
				return nil, err
			}
		}
	}

	return nil, nil
}

func (m *MongoDatabaseImpl) DeleteChannelGroup(channelGroup *ChannelGroup) error {
	ctx := context.Background()
	filter := bson.M{"id": channelGroup.Id}
	_, err := m.channelGroups.DeleteOne(ctx, filter)
	return err
}

func (m *MongoDatabaseImpl) BatchInsertChannelGroups(channelGroups []ChannelGroup) error {
	if len(channelGroups) == 0 {
		return nil
	}

	ctx := context.Background()

	// 为每个channelGroup生成ID并设置默认值
	for i := range channelGroups {
		if channelGroups[i].Id == 0 {
			nextId, err := m.getNextSequenceValue("channel_group_id")
			if err != nil {
				return err
			}
			channelGroups[i].Id = nextId
		}

		// 设置默认值（MongoDB 不支持 schema 级别的默认值）
		// 根据 ChannelGroup 结构体中的 GORM default 标签设置默认值
		if channelGroups[i].Type == 0 {
			channelGroups[i].Type = 0 // default:0 (已经是默认值)
		}
		if channelGroups[i].Status == 0 {
			channelGroups[i].Status = 1 // default:1
		}
		if channelGroups[i].Weight == nil {
			defaultWeight := uint(0) // default:0
			channelGroups[i].Weight = &defaultWeight
		}
		if channelGroups[i].BaseURL == nil {
			defaultBaseURL := "" // default:''
			channelGroups[i].BaseURL = &defaultBaseURL
		}
		if channelGroups[i].Group == "" {
			channelGroups[i].Group = "default" // default:'default'
		}
		if channelGroups[i].Sort == nil {
			defaultSort := 0 // default:0
			channelGroups[i].Sort = &defaultSort
		}
		if channelGroups[i].OverFrequencyAutoDisable == nil {
			defaultOverFrequency := false // default:0
			channelGroups[i].OverFrequencyAutoDisable = &defaultOverFrequency
		}
		if channelGroups[i].RetryInterval == nil {
			defaultRetryInterval := 300 // default:300
			channelGroups[i].RetryInterval = &defaultRetryInterval
		}
		if channelGroups[i].RequestTokenLimitEnabled == nil {
			defaultRequestTokenLimit := false // default:0
			channelGroups[i].RequestTokenLimitEnabled = &defaultRequestTokenLimit
		}
		if channelGroups[i].MinRequestTokenCount == nil {
			defaultMinRequestToken := int64(0) // default:0
			channelGroups[i].MinRequestTokenCount = &defaultMinRequestToken
		}
		if channelGroups[i].MaxRequestTokenCount == nil {
			defaultMaxRequestToken := int64(0) // default:0
			channelGroups[i].MaxRequestTokenCount = &defaultMaxRequestToken
		}
	}

	// 转换为interface{}切片
	var docs []interface{}
	for _, channelGroup := range channelGroups {
		docs = append(docs, channelGroup)
	}

	// 批量插入
	_, err := m.channelGroups.InsertMany(ctx, docs)
	return err
}

func (m *MongoDatabaseImpl) UpdateChannelGroupStatusById(id int, status int) error {
	ctx := context.Background()
	filter := bson.M{"id": id}
	update := bson.M{"$set": bson.M{"status": status}}
	_, err := m.channelGroups.UpdateOne(ctx, filter, update)
	return err
}

func (m *MongoDatabaseImpl) DeleteChannelGroupByStatus(status int64) (int64, error) {
	ctx := context.Background()
	filter := bson.M{"status": status}
	result, err := m.channelGroups.DeleteMany(ctx, filter)
	if err != nil {
		return 0, err
	}
	return result.DeletedCount, nil
}

func (m *MongoDatabaseImpl) DeleteDisabledChannelGroup() (int64, error) {
	ctx := context.Background()
	filter := bson.M{
		"$or": []bson.M{
			{"status": common.ChannelStatusAutoDisabled},
			{"status": common.ChannelStatusManuallyDisabled},
		},
	}
	result, err := m.channelGroups.DeleteMany(ctx, filter)
	if err != nil {
		return 0, err
	}
	return result.DeletedCount, nil
}

// IsTransactionNotSupportedError 检查错误是否是因为MongoDB不支持事务
func IsTransactionNotSupportedError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "Transaction numbers are only allowed on a replica set member or mongos") ||
		strings.Contains(errStr, "transactions are not supported") ||
		strings.Contains(errStr, "replica set") ||
		strings.Contains(errStr, "mongos")
}

// isTransactionNotSupportedError 内部使用的辅助函数
func isTransactionNotSupportedError(err error) bool {
	return IsTransactionNotSupportedError(err)
}

// getNextSequenceValue 获取下一个序列值，用于生成自增ID
func (m *MongoDatabaseImpl) getNextSequenceValue(sequenceName string) (int, error) {
	ctx := context.Background()

	// 使用原子操作：先尝试递增，如果不存在则初始化为1
	filter := bson.M{"_id": sequenceName}
	update := bson.M{"$inc": bson.M{"value": 1}}
	opts := options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)

	var result struct {
		ID    string `bson:"_id"`
		Value int    `bson:"value"`
	}

	err := m.counters.FindOneAndUpdate(ctx, filter, update, opts).Decode(&result)
	if err != nil {
		return 0, err
	}

	return result.Value, nil
}

// InitializeCounters 初始化所有counter到正确的值，用于数据迁移后的修复
func (m *MongoDatabaseImpl) InitializeCounters() error {
	ctx := context.Background()

	sequences := []string{"channel_id", "channel_extend_id", "ability_id", "channel_group_id"}

	for _, sequenceName := range sequences {
		var maxId int

		switch sequenceName {
		case "channel_id":
			// 查找channels集合中的最大ID
			opts := options.FindOne().SetSort(bson.D{bson.E{Key: "id", Value: -1}})
			var maxChannel struct {
				ID int `bson:"id"`
			}
			err := m.channels.FindOne(ctx, bson.M{}, opts).Decode(&maxChannel)
			if err == nil {
				maxId = maxChannel.ID
			}
		case "channel_extend_id":
			// 查找channel_extends集合中的最大ID
			opts := options.FindOne().SetSort(bson.D{bson.E{Key: "id", Value: -1}})
			var maxChannelExtend struct {
				ID int `bson:"id"`
			}
			err := m.channelExs.FindOne(ctx, bson.M{}, opts).Decode(&maxChannelExtend)
			if err == nil {
				maxId = maxChannelExtend.ID
			}
		case "ability_id":
			// 查找abilities集合中的最大ID
			opts := options.FindOne().SetSort(bson.D{bson.E{Key: "id", Value: -1}})
			var maxAbility struct {
				ID int `bson:"id"`
			}
			err := m.abilities.FindOne(ctx, bson.M{}, opts).Decode(&maxAbility)
			if err == nil {
				maxId = maxAbility.ID
			}
		case "channel_group_id":
			// 查找channel_groups集合中的最大ID
			opts := options.FindOne().SetSort(bson.D{bson.E{Key: "id", Value: -1}})
			var maxChannelGroup struct {
				ID int `bson:"id"`
			}
			err := m.channelGroups.FindOne(ctx, bson.M{}, opts).Decode(&maxChannelGroup)
			if err == nil {
				maxId = maxChannelGroup.ID
			}
		}

		if maxId > 0 {
			// 设置counter为最大ID + 1
			filter := bson.M{"_id": sequenceName}
			update := bson.M{"$set": bson.M{"value": maxId}}
			opts := options.Update().SetUpsert(true)

			_, err := m.counters.UpdateOne(ctx, filter, update, opts)
			if err != nil {
				logger.SysError(fmt.Sprintf("Failed to initialize counter %s: %v", sequenceName, err))
				return err
			}

			logger.SysLog(fmt.Sprintf("Initialized counter %s to %d", sequenceName, maxId))
		}
	}

	logger.SysLog("All counters initialized successfully")
	return nil
}

// DeleteChannelExtendsNotExistsInChannel 删除不存在对应 channel 的 channel_extends 记录
func (m *MongoDatabaseImpl) DeleteChannelExtendsNotExistsInChannel() (int64, error) {
	ctx := context.Background()

	// 尝试使用事务，如果失败则使用普通操作
	session, err := m.client.StartSession()
	if err != nil {
		return m.deleteChannelExtendsNotExistsInChannelWithoutTransaction(ctx)
	}
	defer session.EndSession(ctx)

	var deletedCount int64
	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 获取所有存在的 channel ID
		cursor, err := m.channels.Find(sessCtx, bson.M{}, options.Find().SetProjection(bson.M{"id": 1}))
		if err != nil {
			return nil, err
		}
		defer cursor.Close(sessCtx)

		var existingChannelIds []int
		for cursor.Next(sessCtx) {
			var channel struct {
				Id int `bson:"id"`
			}
			if err := cursor.Decode(&channel); err != nil {
				return nil, err
			}
			existingChannelIds = append(existingChannelIds, channel.Id)
		}

		// 删除不存在对应 channel 的 channel_extends 记录
		filter := bson.M{"channel_id": bson.M{"$nin": existingChannelIds}}
		result, err := m.channelExs.DeleteMany(sessCtx, filter)
		if err != nil {
			return nil, err
		}
		deletedCount = result.DeletedCount
		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		// 如果事务失败（可能是因为单机MongoDB不支持事务），使用普通操作
		if isTransactionNotSupportedError(err) {
			return m.deleteChannelExtendsNotExistsInChannelWithoutTransaction(ctx)
		}
		return 0, err
	}
	return deletedCount, nil
}

// deleteChannelExtendsNotExistsInChannelWithoutTransaction 不使用事务的版本
func (m *MongoDatabaseImpl) deleteChannelExtendsNotExistsInChannelWithoutTransaction(ctx context.Context) (int64, error) {
	// 获取所有存在的 channel ID
	cursor, err := m.channels.Find(ctx, bson.M{}, options.Find().SetProjection(bson.M{"id": 1}))
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var existingChannelIds []int
	for cursor.Next(ctx) {
		var channel struct {
			Id int `bson:"id"`
		}
		if err := cursor.Decode(&channel); err != nil {
			return 0, err
		}
		existingChannelIds = append(existingChannelIds, channel.Id)
	}

	// 删除不存在对应 channel 的 channel_extends 记录
	filter := bson.M{"channel_id": bson.M{"$nin": existingChannelIds}}
	result, err := m.channelExs.DeleteMany(ctx, filter)
	if err != nil {
		return 0, err
	}
	return result.DeletedCount, nil
}

func (m *MongoDatabaseImpl) GetChannelStatistics(statusFilter int, disableReason string, groupBy string) ([]*ChannelStatistics, error) {
	ctx := context.Background()

	// 构建聚合管道
	pipeline := []bson.M{}

	// 如果有状态过滤，添加匹配阶段
	if statusFilter != 0 {
		pipeline = append(pipeline, bson.M{
			"$match": bson.M{"status": statusFilter},
		})
	}

	// 构建分组字段
	var groupField interface{}
	switch groupBy {
	case "domain":
		// 按域名分组：从name字段中提取@后面的域名部分
		groupField = bson.M{
			"$cond": []interface{}{
				bson.M{"$regexMatch": bson.M{"input": "$name", "regex": "@"}},
				bson.M{
					"$concat": []interface{}{
						"@",
						bson.M{
							"$arrayElemAt": []interface{}{
								bson.M{"$split": []interface{}{"$name", "@"}},
								1,
							},
						},
					},
				},
				bson.M{"$ifNull": []interface{}{"$remark", "Unknown"}},
			},
		}
	case "server":
		// 按服务器分组：使用remark字段
		groupField = bson.M{"$ifNull": []interface{}{"$remark", "Unknown"}}
	default:
		return nil, fmt.Errorf("unsupported groupBy parameter: %s", groupBy)
	}

	// 添加分组阶段
	groupStage := bson.M{
		"$group": bson.M{
			"_id":        groupField,
			"totalCount": bson.M{"$sum": 1},
			"activeCount": bson.M{
				"$sum": bson.M{
					"$cond": []interface{}{
						bson.M{"$eq": []interface{}{"$status", 1}},
						1,
						0,
					},
				},
			},
			"disabledCount": bson.M{
				"$sum": bson.M{
					"$cond": []interface{}{
						bson.M{"$eq": []interface{}{"$status", 3}},
						1,
						0,
					},
				},
			},
			"unknownCount": bson.M{
				"$sum": bson.M{
					"$cond": []interface{}{
						bson.M{"$not": bson.M{"$in": []interface{}{"$status", []int{1, 3}}}},
						1,
						0,
					},
				},
			},
			"filteredCount": bson.M{
				"$sum": bson.M{
					"$cond": []interface{}{
						bson.M{"$and": []interface{}{
							bson.M{"$eq": []interface{}{"$status", 3}},
							getBanCondition(disableReason),
						}},
						1,
						0,
					},
				},
			},
			"latestActiveCreatedTime": bson.M{
				"$max": bson.M{
					"$cond": []interface{}{
						bson.M{"$eq": []interface{}{"$status", 1}},
						"$created_time",
						nil,
					},
				},
			},
			"averageUsedQuota": bson.M{
				"$avg": "$used_quota",
			},
		},
	}

	// 添加计算阶段
	addFieldsStage := bson.M{
		"$addFields": bson.M{
			"disabledRate": bson.M{
				"$cond": []interface{}{
					bson.M{"$gt": []interface{}{"$totalCount", 0}},
					bson.M{
						"$multiply": []interface{}{
							bson.M{"$divide": []interface{}{"$disabledCount", "$totalCount"}},
							100,
						},
					},
					0,
				},
			},
			"filteredRate": bson.M{
				"$cond": []interface{}{
					bson.M{"$gt": []interface{}{"$totalCount", 0}},
					bson.M{
						"$multiply": []interface{}{
							bson.M{"$divide": []interface{}{"$filteredCount", "$totalCount"}},
							100,
						},
					},
					0,
				},
			},
		},
	}

	// 添加排序阶段
	sortStage := bson.M{
		"$sort": bson.M{
			"disabledCount": -1,
			"totalCount":    -1,
		},
	}

	pipeline = append(pipeline, groupStage, addFieldsStage, sortStage)

	// 执行聚合查询
	cursor, err := m.channels.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 处理结果
	var results []*ChannelStatistics
	for cursor.Next(ctx) {
		var result struct {
			ID                      string  `bson:"_id"`
			TotalCount              int64   `bson:"totalCount"`
			ActiveCount             int64   `bson:"activeCount"`
			DisabledCount           int64   `bson:"disabledCount"`
			UnknownCount            int64   `bson:"unknownCount"`
			FilteredCount           int64   `bson:"filteredCount"`
			DisabledRate            float64 `bson:"disabledRate"`
			FilteredRate            float64 `bson:"filteredRate"`
			LatestActiveCreatedTime *int64  `bson:"latestActiveCreatedTime"`
			AverageUsedQuota        float64 `bson:"averageUsedQuota"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		stat := &ChannelStatistics{
			GroupName:               result.ID,
			TotalCount:              result.TotalCount,
			ActiveCount:             result.ActiveCount,
			DisabledCount:           result.DisabledCount,
			UnknownCount:            result.UnknownCount,
			FilteredCount:           result.FilteredCount,
			DisabledRate:            result.DisabledRate,
			FilteredRate:            result.FilteredRate,
			LatestActiveCreatedTime: result.LatestActiveCreatedTime,
			AverageUsedQuota:        result.AverageUsedQuota,
		}

		results = append(results, stat)
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return results, nil
}

func (m *MongoDatabaseImpl) GetChannelCreationSpeedStatistics(statusFilter int, disableReason string, groupBy string, timeGranularity string) (map[string][]*ChannelCreationSpeedItem, error) {
	ctx := context.Background()
	results := make(map[string][]*ChannelCreationSpeedItem)

	// 构建聚合管道
	pipeline := []bson.M{}

	// 如果有状态过滤，添加匹配阶段
	matchStage := bson.M{}
	if statusFilter != 0 {
		matchStage["status"] = statusFilter
	}

	// 添加禁用原因过滤
	if disableReason != "" && disableReason != "all" {
		matchStage = bson.M{"$and": []bson.M{matchStage, getBanCondition(disableReason)}}
	}

	// 构建时间范围过滤
	now := time.Now()
	var timeRange bson.M
	switch timeGranularity {
	case "hour":
		// 扩大到最近30天，以便包含历史数据
		timeRange = bson.M{"$gte": now.Add(-30 * 24 * time.Hour).Unix()}
	case "day":
		// 扩大到最近90天，以便包含历史数据
		timeRange = bson.M{"$gte": now.Add(-90 * 24 * time.Hour).Unix()}
	default:
		return nil, fmt.Errorf("unsupported timeGranularity parameter: %s", timeGranularity)
	}
	matchStage["created_time"] = timeRange

	if len(matchStage) > 0 {
		pipeline = append(pipeline, bson.M{"$match": matchStage})
	}

	// 构建分组字段
	var groupField interface{}
	switch groupBy {
	case "domain":
		// 按域名分组：从name字段中提取@后面的域名部分
		groupField = bson.M{
			"$cond": []interface{}{
				bson.M{"$regexMatch": bson.M{"input": "$name", "regex": "@"}},
				bson.M{
					"$concat": []interface{}{
						"@",
						bson.M{
							"$arrayElemAt": []interface{}{
								bson.M{"$split": []interface{}{"$name", "@"}},
								1,
							},
						},
					},
				},
				bson.M{"$ifNull": []interface{}{"$remark", "Unknown"}},
			},
		}
	case "server":
		// 按服务器分组：使用remark字段
		groupField = bson.M{"$ifNull": []interface{}{"$remark", "Unknown"}}
	default:
		return nil, fmt.Errorf("unsupported groupBy parameter: %s", groupBy)
	}

	// 构建时间格式化字段，返回时间戳而不是字符串
	var timeFormat interface{}
	switch timeGranularity {
	case "hour":
		// 截断到小时开始，返回秒级时间戳
		timeFormat = bson.M{
			"$divide": []interface{}{
				bson.M{
					"$toLong": bson.M{
						"$dateTrunc": bson.M{
							"date": bson.M{"$toDate": bson.M{"$multiply": []interface{}{"$created_time", 1000}}},
							"unit": "hour",
						},
					},
				},
				1000,
			},
		}
	case "day":
		// 截断到天开始，返回秒级时间戳
		timeFormat = bson.M{
			"$divide": []interface{}{
				bson.M{
					"$toLong": bson.M{
						"$dateTrunc": bson.M{
							"date": bson.M{"$toDate": bson.M{"$multiply": []interface{}{"$created_time", 1000}}},
							"unit": "day",
						},
					},
				},
				1000,
			},
		}
	}

	// 第一阶段：按分组名称和时间点分组统计
	groupStage := bson.M{
		"$group": bson.M{
			"_id": bson.M{
				"group_name": groupField,
				"time_point": timeFormat,
			},
			"creation_count": bson.M{"$sum": 1},
		},
	}

	// 排序阶段
	sortStage := bson.M{
		"$sort": bson.M{
			"_id.group_name": 1,
			"_id.time_point": 1,
		},
	}

	// 第二阶段：计算累计数量
	addFieldsStage := bson.M{
		"$group": bson.M{
			"_id": "$_id.group_name",
			"items": bson.M{
				"$push": bson.M{
					"time_point":     "$_id.time_point",
					"creation_count": "$creation_count",
				},
			},
		},
	}

	pipeline = append(pipeline, groupStage, sortStage, addFieldsStage)

	// 执行聚合查询
	cursor, err := m.channels.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 处理结果
	for cursor.Next(ctx) {
		var result struct {
			ID    string `bson:"_id"`
			Items []struct {
				TimePoint     int64 `bson:"time_point"`
				CreationCount int64 `bson:"creation_count"`
			} `bson:"items"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		// 计算累计数量
		var cumulativeCount int64 = 0
		var items []*ChannelCreationSpeedItem

		for _, item := range result.Items {
			cumulativeCount += item.CreationCount
			speedItem := &ChannelCreationSpeedItem{
				GroupName:       result.ID,
				TimePoint:       item.TimePoint,
				CreationCount:   item.CreationCount,
				CumulativeCount: cumulativeCount,
			}
			items = append(items, speedItem)
		}

		results[result.ID] = items
	}

	if err := cursor.Err(); err != nil {
		return nil, err
	}

	return results, nil
}
