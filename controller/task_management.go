package controller

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/constant"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay"
	"github.com/songquanpeng/one-api/relay/adaptor/vertexai"
	"github.com/songquanpeng/one-api/relay/channel"
	"github.com/songquanpeng/one-api/relay/channeltype"
)

// GetTaskStatus 获取任务状态（不扣费）
func GetTaskStatus(c *gin.Context) {
	taskId := c.Param("task_id")
	userId := c.GetInt("id")

	if taskId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "任务ID不能为空",
		})
		return
	}

	task, exist, err := model.GetByTaskId(userId, taskId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "查询任务失败: " + err.Error(),
		})
		return
	}

	if !exist {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "任务不存在",
		})
		return
	}

	// 构建响应数据
	response := gin.H{
		"id":          task.TaskID,
		"task_id":     task.TaskID, // 前端期望的字段
		"object":      "task",
		"created":     task.CreatedAt,
		"status":      convertTaskStatus(string(task.Status)),
		"task_type":   task.Action,
		"action":      task.Action,           // 前端期望的字段
		"platform":    string(task.Platform), // 前端期望的字段
		"progress":    task.Progress,
		"submit_time": task.SubmitTime,
		"start_time":  task.StartTime,
		"finish_time": task.FinishTime,
		"channel_id":  task.ChannelId, // 前端期望的字段
		"user_id":     task.UserId,    // 前端期望的字段
	}

	// 如果任务成功且有结果URL，添加结果URL
	if task.Status == model.TaskStatusSuccess && task.ResultUrl != "" {
		response["result_url"] = task.ResultUrl
	}

	// 如果任务失败，添加错误信息
	if task.Status == model.TaskStatusFailure && task.FailReason != "" {
		response["error"] = gin.H{
			"message": task.FailReason,
			"type":    "task_failed",
		}
		response["fail_reason"] = task.FailReason // 前端期望的字段
	}

	// 如果任务成功，添加结果数据
	if task.Status == model.TaskStatusSuccess && len(task.Data) > 0 {
		response["result"] = gin.H{
			"data": task.Data,
		}
	}

	c.JSON(http.StatusOK, response)
}

// CancelTask 取消任务
func CancelTask(c *gin.Context) {
	taskId := c.Param("task_id")
	userId := c.GetInt("id")

	if taskId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "任务ID不能为空",
		})
		return
	}

	task, exist, err := model.GetByTaskId(userId, taskId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "查询任务失败: " + err.Error(),
		})
		return
	}

	if !exist {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "任务不存在",
		})
		return
	}

	// 检查任务状态是否可以取消
	if task.Status == model.TaskStatusSuccess || task.Status == model.TaskStatusFailure {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "任务已完成，无法取消",
		})
		return
	}

	// 更新任务状态为取消
	task.Status = model.TaskStatusFailure
	task.FailReason = "用户取消"
	task.FinishTime = time.Now().Unix()

	err = task.Update()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "取消任务失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "任务已取消",
		"data": gin.H{
			"id":     task.TaskID,
			"status": "cancelled",
		},
	})
}

// GetUserTasks 获取用户任务列表
func GetUserTasks(c *gin.Context) {
	userId := c.GetInt("id")

	// 解析分页参数
	page, _ := strconv.Atoi(c.Query("page"))
	if page < 1 {
		page = 1
	}
	pageSize, _ := strconv.Atoi(c.Query("page_size"))
	if pageSize <= 0 {
		pageSize = config.ItemsPerPage
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 解析过滤参数
	taskType := c.Query("task_type")
	status := c.Query("status")

	// 调用模型层查询方法
	tasks, total, err := model.GetUserTasksWithPagination(userId, (page-1)*pageSize, pageSize, taskType, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "查询任务列表失败: " + err.Error(),
		})
		return
	}

	// 转换任务数据格式
	var taskList []gin.H
	for _, task := range tasks {
		taskData := gin.H{
			"id":          task.TaskID,
			"task_id":     task.TaskID, // 前端期望的字段
			"object":      "task",
			"created":     task.CreatedAt,
			"status":      convertTaskStatus(string(task.Status)),
			"task_type":   task.Action,
			"action":      task.Action,           // 前端期望的字段
			"platform":    string(task.Platform), // 前端期望的字段
			"progress":    task.Progress,
			"submit_time": task.SubmitTime,
			"start_time":  task.StartTime,
			"finish_time": task.FinishTime,
			"channel_id":  task.ChannelId, // 前端期望的字段
			"user_id":     task.UserId,    // 前端期望的字段
		}

		// 如果任务成功且有结果URL，添加结果URL
		if task.Status == model.TaskStatusSuccess && task.ResultUrl != "" {
			taskData["result_url"] = task.ResultUrl
		}

		// 如果任务失败，添加错误信息
		if task.Status == model.TaskStatusFailure && task.FailReason != "" {
			taskData["error"] = gin.H{
				"message": task.FailReason,
				"type":    "task_failed",
			}
			taskData["fail_reason"] = task.FailReason // 前端期望的字段
		}

		taskList = append(taskList, taskData)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"items":     taskList,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetTaskStatistics 获取任务统计信息
func GetTaskStatistics(c *gin.Context) {
	userId := c.GetInt("id")

	// 获取任务统计数据
	stats, err := model.GetUserTaskStatistics(userId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    stats,
	})
}

// convertTaskStatus 转换任务状态为标准格式
func convertTaskStatus(status string) string {
	switch status {
	case string(model.TaskStatusNotStart):
		return "pending"
	case string(model.TaskStatusSubmitted):
		return "submitted"
	case string(model.TaskStatusQueued):
		return "queued"
	case string(model.TaskStatusInProgress):
		return "in_progress"
	case string(model.TaskStatusSuccess):
		return "completed"
	case string(model.TaskStatusFailure):
		return "failed"
	default:
		return "unknown"
	}
}

// UpdateTaskBulk 批量更新任务状态（参考new-api实现）
func UpdateTaskBulk() {
	for {
		time.Sleep(time.Duration(15) * time.Second)
		logger.SysLog("任务进度轮询开始")
		ctx := context.TODO()
		allTasks := model.GetAllUnFinishSyncTasks(500)
		platformTask := make(map[constant.TaskPlatform][]*model.Task)
		for _, t := range allTasks {
			platformTask[t.Platform] = append(platformTask[t.Platform], t)
		}
		for platform, tasks := range platformTask {
			if len(tasks) == 0 {
				continue
			}
			taskChannelM := make(map[int][]string)
			taskM := make(map[string]*model.Task)
			nullTaskIds := make([]int64, 0)
			for _, task := range tasks {
				if task.TaskID == "" {
					// 统计失败的未完成任务
					nullTaskIds = append(nullTaskIds, task.ID)
					continue
				}
				taskM[task.TaskID] = task
				taskChannelM[task.ChannelId] = append(taskChannelM[task.ChannelId], task.TaskID)
			}
			if len(nullTaskIds) > 0 {
				err := model.TaskBulkUpdateByID(nullTaskIds, map[string]any{
					"status":   "FAILURE",
					"progress": "100%",
				})
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("Fix null task_id task error: %v", err))
				} else {
					logger.Info(ctx, fmt.Sprintf("Fix null task_id task success: %v", nullTaskIds))
				}
			}
			if len(taskChannelM) == 0 {
				continue
			}

			UpdateTaskByPlatform(platform, taskChannelM, taskM)
		}
		logger.SysLog("任务进度轮询完成")
	}
}

// UpdateTaskByPlatform 根据平台更新任务
func UpdateTaskByPlatform(platform constant.TaskPlatform, taskChannelM map[int][]string, taskM map[string]*model.Task) {
	switch platform {
	case constant.TaskPlatformMidjourney:
		//_ = UpdateMidjourneyTaskAll(context.Background(), tasks)
	case constant.TaskPlatformSuno:
		_ = UpdateSunoTaskAll(context.Background(), taskChannelM, taskM)
	case constant.TaskPlatformKling, constant.TaskPlatformJimeng, constant.TaskPlatformVertex:
		_ = UpdateVideoTaskAll(context.Background(), platform, taskChannelM, taskM)
	default:
		logger.SysLog("未知平台")
	}
}

// UpdateVideoTaskAll 更新视频任务（参考new-api实现）
func UpdateVideoTaskAll(ctx context.Context, platform constant.TaskPlatform, taskChannelM map[int][]string, taskM map[string]*model.Task) error {
	for channelId, taskIds := range taskChannelM {
		if err := updateVideoTaskAll(ctx, platform, channelId, taskIds, taskM); err != nil {
			logger.Error(ctx, fmt.Sprintf("Channel #%d failed to update video async tasks: %s", channelId, err.Error()))
		}
	}
	return nil
}

// updateVideoTaskAll 更新单个渠道的视频任务
func updateVideoTaskAll(ctx context.Context, platform constant.TaskPlatform, channelId int, taskIds []string, taskM map[string]*model.Task) error {
	logger.Info(ctx, fmt.Sprintf("Channel #%d pending video tasks: %d", channelId, len(taskIds)))
	if len(taskIds) == 0 {
		return nil
	}
	cacheGetChannel, err := model.GetChannelById(channelId, true)
	if err != nil {
		errUpdate := model.TaskBulkUpdate(taskIds, map[string]any{
			"fail_reason": fmt.Sprintf("Failed to get channel info, channel ID: %d", channelId),
			"status":      "FAILURE",
			"progress":    "100%",
		})
		if errUpdate != nil {
			logger.SysError(fmt.Sprintf("UpdateVideoTask error: %v", errUpdate))
		}
		return fmt.Errorf("CacheGetChannel failed: %w", err)
	}
	// 使用增强的适配器选择逻辑，考虑渠道类型
	adaptor := relay.GetTaskAdaptorByChannelType(platform, cacheGetChannel.Type)
	if adaptor == nil {
		return fmt.Errorf("video adaptor not found")
	}
	for _, taskId := range taskIds {
		if err := updateVideoSingleTask(ctx, adaptor, cacheGetChannel, taskId, taskM); err != nil {
			logger.Error(ctx, fmt.Sprintf("Failed to update video task %s: %s", taskId, err.Error()))
		}
	}
	return nil
}

// updateVideoSingleTask 更新单个视频任务
func updateVideoSingleTask(ctx context.Context, adaptor channel.TaskAdaptor, channel *model.Channel, taskId string, taskM map[string]*model.Task) error {
	baseURL := ""
	if channel.Type < len(channeltype.ChannelBaseURLs) {
		baseURL = channeltype.ChannelBaseURLs[channel.Type]
	}
	if channel.GetBaseURL() != "" {
		baseURL = channel.GetBaseURL()
	}

	task := taskM[taskId]
	if task == nil {
		logger.Error(ctx, fmt.Sprintf("Task %s not found in taskM", taskId))
		return fmt.Errorf("task %s not found", taskId)
	}

	// 特殊处理Vertex平台的认证
	var resp *http.Response
	var err error

	if task.Platform == constant.TaskPlatformVertex {
		// 对于Vertex平台，需要特殊处理认证
		logger.Info(ctx, fmt.Sprintf("Handling Vertex platform task %s with special authentication", taskId))

		// 解析任务数据获取操作名称
		var taskData map[string]interface{}
		if err := json.Unmarshal(task.Data, &taskData); err != nil {
			return fmt.Errorf("failed to unmarshal task data for task %s: %w", taskId, err)
		}

		// 检查是否为套壳场景（使用 shell.TaskAdaptor）
		isShellAdaptor := (channel.Type == channeltype.ShellAPI || channel.Type == channeltype.Custom)

		if isShellAdaptor {
			// 套壳场景：直接使用适配器的 FetchTask 方法
			logger.Info(ctx, fmt.Sprintf("Using shell adaptor for task %s", taskId))
			resp, err = adaptor.FetchTask(baseURL, channel.Key, map[string]any{
				"task_id": taskId,
				"action":  task.Action,
			})
			if err != nil {
				return fmt.Errorf("fetchTask failed for task %s: %w", taskId, err)
			}
		} else {
			// 直连 Vertex AI 场景：需要 operation name
			operationName, ok := taskData["name"].(string)
			if !ok || operationName == "" {
				return fmt.Errorf("operation name not found in task data for task %s", taskId)
			}

			// 从Channel配置中获取ADC凭据
			var adcJson string
			if channel.Config != "" {
				var config model.ChannelConfig
				if err := json.Unmarshal([]byte(channel.Config), &config); err == nil {
					adcJson = config.VertexAIADC
				}
			}

			// 如果没有ADC凭据，尝试使用API Key
			if adcJson == "" {
				// 检查API Key是否是JSON格式
				var testJson map[string]interface{}
				if err := json.Unmarshal([]byte(channel.Key), &testJson); err == nil {
					adcJson = channel.Key
				}
			}

			if adcJson == "" {
				return fmt.Errorf("no valid authentication found for Vertex task %s", taskId)
			}

			// 获取访问令牌
			token, err := vertexai.GetToken(ctx, channel.Id, adcJson)
			if err != nil {
				return fmt.Errorf("failed to get access token for Vertex task %s: %w", taskId, err)
			}

			// 构建请求URL
			// 从操作名称中提取项目ID、区域和模型ID
			// 格式: projects/{project}/locations/{location}/publishers/google/models/{model}/operations/{operation_id}
			parts := strings.Split(operationName, "/")
			if len(parts) < 6 {
				return fmt.Errorf("invalid operation name format for task %s: %s", taskId, operationName)
			}

			projectID := parts[1]
			location := parts[3]

			// 构建fetchPredictOperation URL
			url := fmt.Sprintf("https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/%s:fetchPredictOperation",
				location, projectID, location, "veo-3.0-generate-preview")

			// 构建请求体
			requestBody := map[string]interface{}{
				"operationName": operationName,
			}
			jsonData, err := json.Marshal(requestBody)
			if err != nil {
				return fmt.Errorf("failed to marshal request body for task %s: %w", taskId, err)
			}

			// 创建请求
			req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
			if err != nil {
				return fmt.Errorf("failed to create request for task %s: %w", taskId, err)
			}

			// 设置请求头
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", "Bearer "+token)

			// 发送请求
			client := &http.Client{Timeout: 30 * time.Second}
			resp, err = client.Do(req)
			if err != nil {
				return fmt.Errorf("failed to send request for task %s: %w", taskId, err)
			}
		}
	} else {
		// 其他平台使用标准方法
		resp, err = adaptor.FetchTask(baseURL, channel.Key, map[string]any{
			"task_id": taskId,
			"action":  task.Action,
		})
		if err != nil {
			return fmt.Errorf("fetchTask failed for task %s: %w", taskId, err)
		}
	}

	defer resp.Body.Close()
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("readAll failed for task %s: %w", taskId, err)
	}

	taskResult, err := adaptor.ParseTaskResult(responseBody)
	if err != nil {
		return fmt.Errorf("parseTaskResult failed for task %s: %w", taskId, err)
	}

	now := time.Now().Unix()
	if taskResult.Status == "" {
		return fmt.Errorf("task %s status is empty", taskId)
	}
	task.Status = model.TaskStatus(taskResult.Status)
	switch task.Status {
	case model.TaskStatusSubmitted:
		task.Progress = "10%"
	case model.TaskStatusQueued:
		task.Progress = "20%"
	case model.TaskStatusInProgress:
		task.Progress = "30%"
		if task.StartTime == 0 {
			task.StartTime = now
		}
	case model.TaskStatusSuccess:
		task.Progress = "100%"
		if task.FinishTime == 0 {
			task.FinishTime = now
		}
		task.ResultUrl = taskResult.Url
		fmt.Printf("Task %s completed successfully, URL: %s\n", taskId, taskResult.Url)
	case model.TaskStatusFailure:
		task.Status = model.TaskStatusFailure
		task.Progress = "100%"
		if task.FinishTime == 0 {
			task.FinishTime = now
		}
		task.FailReason = taskResult.Reason
		logger.Info(ctx, fmt.Sprintf("Task %s failed: %s", task.TaskID, task.FailReason))

		// 完善退费逻辑 - 参考new-api的成功案例
		quota := int64(task.Quota)
		if quota != 0 {
			// 增加用户配额（退费）
			if err := model.IncreaseUserQuota(task.UserId, quota); err != nil {
				logger.Error(ctx, "Failed to increase user quota: "+err.Error())
			} else {
				// 抵消渠道消耗和用户消耗
				model.UpdateUserUsedQuotaAndRequestCount(task.UserId, -quota)
				model.UpdateChannelUsedQuota(task.ChannelId, -quota)

				// 记录退费日志
				logContent := fmt.Sprintf("异步任务失败 %s，退费 %s", task.TaskID, common.LogQuota(quota))

				// 记录详细的退费日志
				model.RecordRefundLogByDetailIfZeroQuota(
					ctx,
					"", // requestId - 异步任务没有原始requestId
					"", // ip
					"", // remoteIp
					"", // xForwardedFor - 异步任务没有HTTP头信息
					"", // xRealIp - 异步任务没有HTTP头信息
					"", // cfConnectingIp - 异步任务没有HTTP头信息
					task.UserId,
					task.ChannelId,
					0,                  // inputTokens
					0,                  // outputTokens
					"video-generation", // modelName
					"",                 // tokenName
					"",                 // tokenKey
					"",                 // tokenGroup
					int(-quota),        // quota (负数表示退费)
					0,                  // costQuota
					0,                  // requestDuration
					0,                  // responseFirstByteDuration
					false,              // success
					logContent,
				)

				logger.Info(ctx, fmt.Sprintf("异步任务失败退费成功，任务ID: %s，退费配额: %s", task.TaskID, common.LogQuota(quota)))
			}
		}
	default:
		return fmt.Errorf("unknown task status %s for task %s", taskResult.Status, taskId)
	}
	// Progress字段在TaskInfo中不存在，使用默认进度更新

	// 对于Vertex平台，不要覆盖原始任务数据，只更新状态相关字段
	if task.Platform != constant.TaskPlatformVertex {
		task.Data = responseBody
	}

	if err := task.Update(); err != nil {
		logger.SysError("UpdateVideoTask task error: " + err.Error())
		return fmt.Errorf("failed to update task %s in database: %w", taskId, err)
	}

	logger.Info(ctx, fmt.Sprintf("Task %s updated successfully: status=%s, progress=%s", taskId, task.Status, task.Progress))

	return nil
}

// UpdateSunoTaskAll 更新Suno任务（简化实现）
func UpdateSunoTaskAll(ctx context.Context, taskChannelM map[int][]string, taskM map[string]*model.Task) error {
	// 简化实现，暂时跳过Suno任务更新
	logger.Info(ctx, "Suno task update skipped for now")
	return nil
}
