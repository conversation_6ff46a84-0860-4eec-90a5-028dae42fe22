package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/model"
	"gorm.io/gorm"
)

func main() {
	// 初始化数据库
	err := common.Init()
	if err != nil {
		log.Fatalf("Failed to initialize common: %v", err)
	}

	db, err := model.InitDB("SQL_DSN")
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	fmt.Println("=== 长分组字段测试脚本 ===")
	fmt.Println()

	// 生成测试用的长分组名称
	longGroupName := "test_very_long_group_name_that_exceeds_32_characters_but_within_512_limit_" +
		strings.Repeat("x", 100)

	if len(longGroupName) > 500 {
		longGroupName = longGroupName[:500]
	}

	fmt.Printf("测试分组名称长度: %d 字符\n", len(longGroupName))
	fmt.Printf("分组名称: %s\n", longGroupName)
	fmt.Println()

	// 测试 1: 验证数据库字段长度
	fmt.Println("1. 验证数据库字段长度...")
	if err := verifyFieldLengths(db); err != nil {
		fmt.Printf("❌ 字段长度验证失败: %v\n", err)
	} else {
		fmt.Println("✅ 字段长度验证通过")
	}
	fmt.Println()

	// 测试 2: 测试 Ability 表
	fmt.Println("2. 测试 Ability 表...")
	if err := testAbilityLongGroup(longGroupName); err != nil {
		fmt.Printf("❌ Ability 表测试失败: %v\n", err)
	} else {
		fmt.Println("✅ Ability 表测试通过")
	}
	fmt.Println()

	// 测试 3: 测试 Channel 表
	fmt.Println("3. 测试 Channel 表...")
	if err := testChannelLongGroup(longGroupName); err != nil {
		fmt.Printf("❌ Channel 表测试失败: %v\n", err)
	} else {
		fmt.Println("✅ Channel 表测试通过")
	}
	fmt.Println()

	// 测试 4: 测试 ChannelGroup 表
	fmt.Println("4. 测试 ChannelGroup 表...")
	if err := testChannelGroupLongGroup(longGroupName); err != nil {
		fmt.Printf("❌ ChannelGroup 表测试失败: %v\n", err)
	} else {
		fmt.Println("✅ ChannelGroup 表测试通过")
	}
	fmt.Println()

	// 测试 5: 测试搜索性能
	fmt.Println("5. 测试搜索性能...")
	if err := testSearchPerformance(longGroupName); err != nil {
		fmt.Printf("❌ 搜索性能测试失败: %v\n", err)
	} else {
		fmt.Println("✅ 搜索性能测试通过")
	}
	fmt.Println()

	fmt.Println("=== 测试完成 ===")
}

func verifyFieldLengths(db *gorm.DB) error {
	tables := []struct {
		name   string
		column string
	}{
		{"abilities", "group"},
		{"channels", "group"},
		{"channel_groups", "group"},
	}

	for _, table := range tables {
		var columnType string
		var query string

		if common.UsingPostgreSQL {
			query = `SELECT data_type FROM information_schema.columns 
					WHERE table_name = ? AND column_name = ?`
		} else {
			query = `SELECT COLUMN_TYPE FROM information_schema.columns 
					WHERE table_schema = DATABASE() AND table_name = ? AND column_name = ?`
		}

		err := db.Raw(query, table.name, table.column).Scan(&columnType).Error
		if err != nil {
			return fmt.Errorf("failed to check %s.%s: %v", table.name, table.column, err)
		}

		fmt.Printf("  %s.%s: %s\n", table.name, table.column, columnType)

		// 检查是否支持长字符串
		if common.UsingPostgreSQL {
			if !strings.Contains(strings.ToLower(columnType), "character varying") {
				return fmt.Errorf("%s.%s is not varchar type", table.name, table.column)
			}
		} else {
			if !strings.Contains(strings.ToLower(columnType), "varchar") {
				return fmt.Errorf("%s.%s is not varchar type", table.name, table.column)
			}
		}
	}

	return nil
}

func testAbilityLongGroup(longGroupName string) error {
	// 创建测试能力
	ability := &model.Ability{
		Group:     longGroupName,
		Model:     "test-model-" + time.Now().Format("150405"),
		ChannelId: 999999,
		Enabled:   &[]bool{true}[0],
	}

	// 插入
	err := ability.Insert()
	if err != nil {
		return fmt.Errorf("failed to insert: %v", err)
	}

	// 清理
	defer func() {
		ability.Delete()
	}()

	// 验证查询
	retrieved, err := model.GetAbilityByGroupModelChannel(longGroupName, ability.Model, 999999)
	if err != nil {
		return fmt.Errorf("failed to retrieve: %v", err)
	}

	if retrieved.Group != longGroupName {
		return fmt.Errorf("group mismatch: expected %s, got %s", longGroupName, retrieved.Group)
	}

	fmt.Printf("  ✓ 成功存储和查询长度为 %d 的分组名称\n", len(longGroupName))
	return nil
}

func testChannelLongGroup(longGroupName string) error {
	// 创建测试渠道
	channel := &model.Channel{
		Name:        "test-channel-" + time.Now().Format("150405"),
		Type:        1,
		Key:         "test-key-" + time.Now().Format("20060102150405"),
		Group:       longGroupName,
		Models:      "gpt-3.5-turbo",
		Status:      common.ChannelStatusEnabled,
		CreatedTime: time.Now().Unix(),
	}

	// 插入
	err := channel.Insert()
	if err != nil {
		return fmt.Errorf("failed to insert: %v", err)
	}

	// 清理
	defer func() {
		channel.Delete()
	}()

	// 验证查询
	retrieved, err := model.GetChannelById(channel.Id, false)
	if err != nil {
		return fmt.Errorf("failed to retrieve: %v", err)
	}

	if retrieved.Group != longGroupName {
		return fmt.Errorf("group mismatch: expected %s, got %s", longGroupName, retrieved.Group)
	}

	fmt.Printf("  ✓ 成功存储和查询长度为 %d 的分组名称\n", len(longGroupName))
	return nil
}

func testChannelGroupLongGroup(longGroupName string) error {
	// 创建测试渠道组
	channelGroup := &model.ChannelGroup{
		Name:        "test-group-" + time.Now().Format("150405"),
		Group:       longGroupName,
		Models:      "gpt-3.5-turbo",
		Status:      common.ChannelStatusEnabled,
		CreatedTime: time.Now().Unix(),
	}

	// 插入
	err := channelGroup.Insert()
	if err != nil {
		return fmt.Errorf("failed to insert: %v", err)
	}

	// 清理
	defer func() {
		channelGroup.Delete()
	}()

	// 验证查询
	retrieved, err := model.GetChannelGroupById(channelGroup.Id)
	if err != nil {
		return fmt.Errorf("failed to retrieve: %v", err)
	}

	if retrieved.Group != longGroupName {
		return fmt.Errorf("group mismatch: expected %s, got %s", longGroupName, retrieved.Group)
	}

	fmt.Printf("  ✓ 成功存储和查询长度为 %d 的分组名称\n", len(longGroupName))
	return nil
}

func testSearchPerformance(longGroupName string) error {
	// 创建测试数据
	ability := &model.Ability{
		Group:     longGroupName,
		Model:     "perf-test-" + time.Now().Format("150405"),
		ChannelId: 999997,
		Enabled:   &[]bool{true}[0],
	}

	err := ability.Insert()
	if err != nil {
		return fmt.Errorf("failed to insert test data: %v", err)
	}

	defer ability.Delete()

	// 测试搜索性能
	start := time.Now()
	abilities, err := model.GetAllAbilities(0, 100, "", longGroupName, "", 0, false)
	if err != nil {
		return fmt.Errorf("search failed: %v", err)
	}
	duration := time.Since(start)

	found := false
	for _, ab := range abilities {
		if ab.Group == longGroupName && ab.Model == ability.Model {
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("test data not found in search results")
	}

	fmt.Printf("  ✓ 搜索耗时: %v, 找到 %d 条结果\n", duration, len(abilities))
	return nil
}
