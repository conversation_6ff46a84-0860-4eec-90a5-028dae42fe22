import React, {lazy, ReactNode, useContext, useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {useTranslation} from 'react-i18next';
import {useLocation, useNavigate, useSearchParams} from 'react-router-dom';
import {
    AutoComplete,
    Button,
    Card,
    Col,
    Collapse,
    Empty,
    message as AntdMessage,
    Modal,
    Popconfirm,
    Popover,
    Row,
    Select,
    Space,
    Spin,
    Switch,
    Tag,
    Tooltip,
    Typography,
    Table,
    Form,
    DatePicker,
    InputNumber,
    Input,
    Drawer,
    Checkbox,
    Divider,
    Badge,
    Dropdown
} from 'antd';
import type { ColumnsType, TableProps } from 'antd/es/table';
import type { FormInstance } from 'antd/es/form';
import { Resizable } from 'react-resizable';
import type { ResizableProps } from 'react-resizable';
import 'react-resizable/css/styles.css';
import {
    API,
    hasPermission,
    isAdmin,
    isMobile,
    Permission,
    showError,
    timestamp2string
} from '../../helpers';
import {createRenderFunctions, renderModelTags,} from '../../helpers/render';
import moment from 'moment';
import dayjs, { Dayjs } from 'dayjs';
import {
    ClockCircleFilled,
    ControlFilled,
    FileExcelFilled,
    IdcardFilled,
    InfoCircleOutlined,
    LoadingOutlined,
    LockFilled,
    SearchOutlined,
    ReloadOutlined,
    SettingOutlined,
    ClearOutlined,
    DownOutlined,
    FilterOutlined,
    SortAscendingOutlined,
    SortDescendingOutlined,
    EyeOutlined,
    EyeInvisibleOutlined
} from '@ant-design/icons';
import Papa from 'papaparse';
import {
    AUTOCOMPLETE_ERROR_CODES,
    AUTOCOMPLETE_MODEL_NAMES,
    LOGS_TYPE,
    LOGS_TYPE_OPTIONS,
    paginationProps
} from "../../constants";
import {UserContext} from "../../context/User";
import {StatusContext} from "../../context/Status";
import DailyUsageByModelEcharts from "../Chart/DailyUsageModelEcharts";
import {isInternalIP} from '../../utils/ipUtils';
import LogContentDetail from "./LogContentDetail";
import {getNumberDecimalPlaces} from '../../utils/numberUtils';
import ModelUsagePie from '../Chart/ModelUsagePie';
import {ConsumptionMetricsPanel} from '../Chart/Metrics';

const {Meta} = Card;
const {Text} = Typography;
const {RangePicker} = DatePicker;
const LogDetailDrawer = lazy(() => import('./LogDetailDrawer'));

// 添加高亮文本的辅助函数
const HighlightText = ({text, searchText}: { text: string, searchText: string }) => {
    if (!searchText) return <>{text}</>;

    const parts = text.split(new RegExp(`(${searchText})`, 'gi'));
    return (
        <>
            {parts.map((part, index) =>
                part.toLowerCase() === searchText.toLowerCase() ?
                    <Text key={index} mark>{part}</Text> :
                    <span key={index}>{part}</span>
            )}
        </>
    );
};

// 接口定义
interface DailyModelUsageStat {
    sumQuota: number;
    [key: string]: any;
}

interface GroupOption {
    id: number;
    name: string;
    display_name: string;
    description?: string;
    convert_ratio: number;
    current_group_ratio: number;
    current_topup_ratio: number;
    color_mapping?: string;
}

interface LogParams {
    current?: number;
    pageSize?: number;
    start_timestamp?: number;
    end_timestamp?: number;
    [key: string]: any;
}

interface Stat {
    quota: number;
    token: number;
    rpm: number;
    tpm: number;
    mpm: number;
    is_realtime_data?: boolean;
}

interface RequestLog {
    id: number;
    created_at: number;
    model_name: string;
    token_name: string;
    quota: number;
    error_code?: string;
    content: string;
}

interface ResizableLogsTableProps {
    useResizableTable?: boolean;
    setUseResizableTable?: (value: boolean) => void;
}

interface SearchFormValues {
    created_time?: [Dayjs, Dayjs];
    type?: string[];
    channel?: string;
    channel_name?: string;
    username?: string;
    token_name?: string;
    token_group?: string;
    model_name?: string;
    request_id?: string;
    error_code?: string;
    exclude_models?: string[];
    exclude_error_codes?: string[];
    prompt_tokens_min?: number;
    prompt_tokens_max?: number;
    completion_tokens_min?: number;
    completion_tokens_max?: number;
    quota_min?: number;
    quota_max?: number;
    request_duration_min?: number;
    request_duration_max?: number;
    response_first_byte_duration_min?: number;
    response_first_byte_duration_max?: number;
    total_duration_min?: number;
    total_duration_max?: number;
    is_stream?: string;
    ip?: string;
    token_key?: string;
    sort_order?: string;
    dimension?: string;
}

// 可调整大小的表头组件
const ResizableTitle = (props: any) => {
    const {onResize, width, ...restProps} = props;

    if (!width) {
        return <th {...restProps} />;
    }

    const ResizableComponent = Resizable as any;

    return (
        <ResizableComponent
            width={width}
            height={0}
            handle={(
                <span
                    className="react-resizable-handle"
                    style={{
                        position: 'absolute',
                        right: -5,
                        bottom: 0,
                        top: 0,
                        width: 10,
                        cursor: 'col-resize',
                        zIndex: 1,
                        backgroundColor: 'transparent'
                    }}
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                />
            )}
            onResize={onResize}
            draggableOpts={{enableUserSelectHack: false}}
        >
            <th {...restProps} />
        </ResizableComponent>
    );
};

// Memoized饼图组件
const MemoizedPie = React.memo(({data}: { data: any[] }) => {
    return <ModelUsagePie data={data}/>;
}, (prevProps, nextProps) => {
    return JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data);
});

const ResizableLogsTable: React.FC<ResizableLogsTableProps> = ({ 
    useResizableTable, 
    setUseResizableTable 
}) => {
    const {t} = useTranslation();
    const {
        renderModels,
        renderQuota,
        renderRoleTag,
        renderQuotaExpireTime,
        renderChannelStatusTag,
        renderIsStream,
        renderRequestDuration,
        renderResponseFirstByteDuration,
        renderTotalDuration
    } = createRenderFunctions(t);

    // 状态管理
    const [logs, setLogs] = useState([]);
    const [userState] = useContext(UserContext);
    const [statusState] = useContext(StatusContext);
    const location = useLocation();
    const navigate = useNavigate();
    const [searchParams, setSearchParams] = useSearchParams();
    
    // 数据相关状态
    const [lastParams, setLastParams] = useState<LogParams | null>(null);
    const [modelUsageData, setModelUsageData] = useState([]);
    const [loadingModelUsageData, setLoadingModelUsageData] = useState(true);
    const [logsCount, setLogsCount] = useState(0);
    const [stat, setStat] = useState<Stat>({quota: 0, token: 0, rpm: 0, tpm: 0, mpm: 0});
    const [dailyModelUsageStats, setDailyModelUsageStats] = useState([] as DailyModelUsageStat[]);
    const [loadingDailyModelUsageStats, setLoadingDailyModelUsageStats] = useState(true);
    const [needGetSata, setNeedGetSata] = useState(statusState.customConfig.CustomAutoGetLogStat);
    const [loading, setLoading] = useState(true);
    const [loadingStats, setLoadingStats] = useState(false);
    const [loadingTotalQuota, setLoadingTotalQuota] = useState(false);
    const [groupOptions, setGroupOptions] = useState<GroupOption[]>([]);

    // 分组筛选相关状态
    const [groupSearchText, setGroupSearchText] = useState('');
    const [debouncedGroupSearchText, setDebouncedGroupSearchText] = useState('');
    const [expandedGroupOptions, setExpandedGroupOptions] = useState<{[key: string]: boolean}>({});

    // 防抖处理搜索文本
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedGroupSearchText(groupSearchText);
        }, 200);

        return () => clearTimeout(timer);
    }, [groupSearchText]);

    // 优化过滤逻辑 - 使用防抖后的搜索文本和useMemo缓存过滤结果
    const filteredGroupOptions = useMemo(() => {
        if (!debouncedGroupSearchText) return groupOptions;

        const searchText = debouncedGroupSearchText.toLowerCase();
        return groupOptions.filter(group =>
            group.name.toLowerCase().includes(searchText) ||
            group.display_name.toLowerCase().includes(searchText) ||
            group.description?.toLowerCase().includes(searchText) ||
            `${group.convert_ratio}`.includes(searchText) ||
            (group.convert_ratio > 1 ? '加价' : '折扣').includes(searchText)
        );
    }, [groupOptions, debouncedGroupSearchText]);

    // UI相关状态
    const [customShowDailyModelUsageStats, setCustomShowDailyModelUsageStats] = useState(
        localStorage.getItem('customShowDailyModelUsageStats') === 'true'
    );
    const [durationType, setDurationType] = useState("2");
    const [searchVisible, setSearchVisible] = useState(false);
    const [columnsVisible, setColumnsVisible] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [currentContent, setCurrentContent] = useState('');
    const [showLogDetailDrawerLogId, setShowLogDetailDrawerLogId] = useState(0);
    const [isLogDetailDrawerVisible, setIsLogDetailDrawerVisible] = useState(false);
    const [isRequestLogsModalVisible, setIsRequestLogsModalVisible] = useState(false);
    const [currentRequestId, setCurrentRequestId] = useState('');
    const [requestLogs, setRequestLogs] = useState<RequestLog[]>([]);
    const [loadingRequestLogs, setLoadingRequestLogs] = useState(false);
    
    // 自动刷新相关状态
    const [autoRefreshInterval, setAutoRefreshInterval] = useState(0);
    const [refreshTimer, setRefreshTimer] = useState<NodeJS.Timeout | null>(null);
    
    // 用户配置状态
    const defaultTimeTodayOnly = statusState.customConfig.CustomDefaultTimeTodayOnly || false;
    const usageStatsDefaultTimeUnit = statusState.customConfig.CustomUsageStatsDefaultTimeUnit || 'day';
    const [userOnlyToday, setUserOnlyToday] = useState(localStorage.getItem('userOnlyToday') === 'true');
    const [userTimeUnit, setUserTimeUnit] = useState(localStorage.getItem('userTimeUnit') || 'day');
    
    // 表格配置状态
    const [columnWidths, setColumnWidths] = useState(() => {
        try {
            const saved = localStorage.getItem('resizableLogsTableColumnWidths');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            console.error('解析保存的列宽配置失败:', e);
            return {};
        }
    });

    const [visibleColumns, setVisibleColumns] = useState(() => {
        try {
            const saved = localStorage.getItem('resizableLogsTableVisibleColumns');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            console.error('解析保存的列显示配置失败:', e);
            return {};
        }
    });
    
    // 分页状态
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 20,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number, range: [number, number]) => 
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        pageSizeOptions: ['10', '20', '50', '100'],
    });

    // 搜索表单
    const [searchForm] = Form.useForm<SearchFormValues>();
    
    // 时间相关计算
    const finalDefaultTimeTodayOnly = userOnlyToday !== null ? userOnlyToday : defaultTimeTodayOnly;
    const finalUsageStatsDefaultTimeUnit = userTimeUnit || usageStatsDefaultTimeUnit;
    
    const query = new URLSearchParams(location.search);
    const customLogQueryTime = statusState.customConfig.CustomLogQueryDuration;
    const start_timestamp = Number(query.get('start_timestamp')) || 
        (finalDefaultTimeTodayOnly ? moment().startOf('day').unix() :  
        (customLogQueryTime !== 0 ? moment().subtract(customLogQueryTime - 1, 'days').startOf('day').unix() : 0));
    const end_timestamp = Number(query.get('end_timestamp')) || 
        (finalDefaultTimeTodayOnly ? moment().endOf('day').unix() :  
        (customLogQueryTime !== 0 ? moment().endOf('day').unix() : 0));
    const startTime = start_timestamp !== 0 ? moment.unix(start_timestamp) : undefined;
    const endTime = end_timestamp !== 0 ? moment.unix(end_timestamp) : undefined;

    // 初始化
    useEffect(() => {
        fetchGroups();
        // 设置初始搜索条件
        if (startTime && endTime) {
            searchForm.setFieldsValue({
                created_time: [dayjs(startTime.toDate()), dayjs(endTime.toDate())]
            });
        }
        // 初始加载数据
        const params = getSearchParams();
        loadData(params);
    }, []);

    // 监听用户配置变化
    useEffect(() => {
        localStorage.setItem('userOnlyToday', userOnlyToday ? 'true' : 'false');
    }, [userOnlyToday]);
    
    useEffect(() => {
        localStorage.setItem('userTimeUnit', userTimeUnit);
    }, [userTimeUnit]);

    // 清理定时器
    useEffect(() => {
        return () => {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
        };
    }, [refreshTimer]);

    // 获取分组数据
    const fetchGroups = async () => {
        try {
            const res = await API.get('/api/groupPro/selectable?p=0&pageSize=100');
            const {success, data} = res.data;
            if (success) {
                setGroupOptions(data);
            } else {
                try {
                    const res = await API.get('/api/group/list');
                    const {success, data} = res.data;
                    if (success) {
                        setGroupOptions(data);
                    }
                } catch (backupError) {
                    showError(backupError);
                }
            }
        } catch (error) {
            console.error('Error fetching groups:', error);
        }
    };

    // 处理分组选项展开/收起
    const toggleGroupExpand = (groupName: string, e: React.MouseEvent) => {
        e.stopPropagation();
        setExpandedGroupOptions(prev => ({
            ...prev,
            [groupName]: !prev[groupName]
        }));
    };

    // 保存配置到localStorage
    const saveColumnWidths = (widths: any) => {
        try {
            localStorage.setItem('resizableLogsTableColumnWidths', JSON.stringify(widths));
        } catch (e) {
            console.error('保存列宽配置失败:', e);
        }
    };

    const saveVisibleColumns = (visible: any) => {
        try {
            localStorage.setItem('resizableLogsTableVisibleColumns', JSON.stringify(visible));
        } catch (e) {
            console.error('保存列显示配置失败:', e);
        }
    };

    // 处理列宽调整
    const handleResize = (index: number) => (e: any, {size}: any) => {
        const newWidths = {...columnWidths};
        const allColumns = isAdmin(userState) ? getAdminColumns() : getUserColumns();
        const columnKey = allColumns[index]?.key || allColumns[index]?.dataIndex;
        if (columnKey) {
            newWidths[columnKey] = size.width;
            setColumnWidths(newWidths);
            saveColumnWidths(newWidths);
        }
    };

    // 处理列显示/隐藏
    const handleColumnVisibleChange = (columnKey: string, visible: boolean) => {
        const newVisible = {...visibleColumns, [columnKey]: visible};
        setVisibleColumns(newVisible);
        saveVisibleColumns(newVisible);
    };

    // 获取搜索参数
    const getSearchParams = useCallback((): LogParams => {
        const values = searchForm.getFieldsValue();
        const params: LogParams = {
            current: pagination.current,
            pageSize: pagination.pageSize,
        };

        // 处理时间范围
        if (values.created_time && values.created_time.length === 2) {
            params.start_timestamp = values.created_time[0].unix();
            params.end_timestamp = values.created_time[1].unix();
        }

        // 处理其他搜索条件
        Object.keys(values).forEach(key => {
            const value = values[key as keyof SearchFormValues];
            if (value !== undefined && value !== null && value !== '' && 
                (!Array.isArray(value) || value.length > 0)) {
                params[key] = value;
            }
        });

        return params;
    }, [searchForm, pagination]);

    // 数据加载函数
    const loadData = async (params: LogParams) => {
        setLoading(true);
        try {
            const {current, pageSize, ...rest} = params;
            let query = '';
            Object.keys(rest).forEach(key => {
                if (rest[key] !== undefined && rest[key] !== null) {
                    if (Array.isArray(rest[key])) {
                        query += `&${key}=${rest[key].join(',')}`;
                    } else {
                        query += `&${key}=${rest[key]}`;
                    }
                }
            });

            let url: string;
            if (isAdmin(userState)) {
                url = `/api/log/?p=${(current || 1) - 1}&pageSize=${pageSize || 20}${query}`;
            } else {
                url = `/api/log/self/?p=${(current || 1) - 1}&pageSize=${pageSize || 20}${query}`;
            }

            const res = await API.get(url);
            const {success, data} = res.data;
            
            if (success) {
                setLogs(data);
                setLastParams(params);
                
                // 并行加载其他数据
                Promise.all([
                    getLogCount(params),
                    needGetSata ? (isAdmin(userState) ? getLogStat(params) : getLogSelfStat(params)) : Promise.resolve(),
                    fetchStatsData(params)
                ]).catch(error => {
                    console.error('加载附加数据失败:', error);
                });
            } else {
                showError('加载数据失败');
            }
        } catch (error) {
            showError(error as any);
            setLogs([]);
        } finally {
            setLoading(false);
        }
    };

    // 获取日志数量
    const getLogCount = async (params: LogParams) => {
        try {
            const {current, pageSize, ...rest} = params;
            let query = '';
            Object.keys(rest).forEach(key => {
                if (rest[key] !== undefined && rest[key] !== null) {
                    query += `&${key}=${rest[key]}`;
                }
            });

            let url: string;
            if (isAdmin(userState)) {
                url = `/api/log/count/?${query}`;
            } else {
                url = `/api/log/self/count?${query}`;
            }

            const res = await API.get(url);
            const {success, data} = res.data;
            if (success) {
                setLogsCount(data.count);
                setPagination(prev => ({...prev, total: data.count}));
            }
        } catch (error) {
            console.error('获取日志数量失败:', error);
        }
    };

    // 获取统计数据
    const getLogStat = async (params: LogParams, force = false) => {
        if (!needGetSata && !force) return;
        setLoadingStats(true);
        try {
            const {current, pageSize, ...rest} = params;
            let queryParams = {...rest};
            if (start_timestamp && !isNaN(Number(start_timestamp))) {
                queryParams.start_timestamp = Number(start_timestamp);
            }
            if (end_timestamp && !isNaN(Number(end_timestamp))) {
                queryParams.end_timestamp = Number(end_timestamp);
            }
            
            let query = '';
            Object.keys(queryParams).forEach(key => {
                if (queryParams[key] !== undefined && queryParams[key] !== null) {
                    query += `&${key}=${queryParams[key]}`;
                }
            });
            
            const res = await API.get(`/api/log/stat?${query}`);
            const {success, data} = res.data;
            if (success) setStat(data);
        } finally {
            setLoadingStats(false);
        }
    };

    const getLogSelfStat = async (params: LogParams, force = false) => {
        if (!needGetSata && !force) return;
        setLoadingStats(true);
        try {
            const {current, pageSize, ...rest} = params;
            let queryParams = {...rest};
            if (start_timestamp && !isNaN(Number(start_timestamp))) {
                queryParams.start_timestamp = Number(start_timestamp);
            }
            if (end_timestamp && !isNaN(Number(end_timestamp))) {
                queryParams.end_timestamp = Number(end_timestamp);
            }
            
            let query = '';
            Object.keys(queryParams).forEach(key => {
                if (queryParams[key] !== undefined && queryParams[key] !== null) {
                    query += `&${key}=${queryParams[key]}`;
                }
            });
            
            const res = await API.get(`/api/log/self/stat?${query}`);
            const {success, data} = res.data;
            if (success) setStat(data);
        } finally {
            setLoadingStats(false);
        }
    };

    // 获取模型使用情况
    const getModelUsage = async (params: LogParams = {}) => {
        const {current, pageSize, ...rest} = params;
        let query = '';
        Object.keys(rest).forEach(key => {
            query += `&${key}=${rest[key]}`;
        });
        
        const useNewDataApi = statusState.status.DataExportEnabled &&
            statusState.status.DataExportDisplayEnabled;
        let url;
        if (useNewDataApi) {
            url = `/api/data/model_stats?${query}`;
        } else {
            url = isAdmin(userState)
                ? `/api/log/model_usage?${query}`
                : `/api/log/self/model_usage?${query}`;
        }
        
        const res = await API.get(url);
        const {success, message, data} = res.data;
        if (success) {
            return data;
        } else {
            showError(message);
        }
    };

    // 获取统计图表数据
    const fetchStatsData = async (params: LogParams, reload = false) => {
        setLoadingDailyModelUsageStats(true);
        setLoadingModelUsageData(true);
        if (!customShowDailyModelUsageStats && !reload) {
            return Promise.resolve();
        }
        try {
            const modelUsageData = await getModelUsage(params ?? {});
            setModelUsageData(modelUsageData?.map(item => ({type: item.modelName, value: item.cnt})));
            setLoadingModelUsageData(false);
        } finally {
            setLoadingDailyModelUsageStats(false);
        }
    };

    // 处理搜索
    const handleSearch = (values: SearchFormValues) => {
        const params: any = {
            ...values,
            current: 1,
            pageSize: pagination.pageSize,
        };
        
        // 处理时间范围
        if (values.created_time && values.created_time.length === 2) {
            params.start_timestamp = values.created_time[0].unix();
            params.end_timestamp = values.created_time[1].unix();
            delete params.created_time;
        }
        
        // 更新URL参数
        const searchParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
            const value = params[key];
            if (value !== undefined && value !== null && value !== '' && 
                (!Array.isArray(value) || value.length > 0)) {
                searchParams.set(key, Array.isArray(value) ? value.join(',') : String(value));
            }
        });
        setSearchParams(searchParams);
        
        setPagination(prev => ({...prev, current: 1}));
        loadData(params);
        setSearchVisible(false);
    };

    // 重置搜索
    const handleReset = () => {
        searchForm.resetFields();
        if (startTime && endTime) {
            searchForm.setFieldsValue({
                created_time: [dayjs(startTime.toDate()), dayjs(endTime.toDate())]
            });
        }
        const params = {
            current: 1,
            pageSize: pagination.pageSize,
        };
        setSearchParams(new URLSearchParams());
        setPagination(prev => ({...prev, current: 1}));
        loadData(params);
    };

    // 处理表格变化（分页、排序等）
    const handleTableChange: TableProps<any>['onChange'] = (paginationProps, filters, sorter, extra) => {
        const newPagination = {
            ...pagination,
            current: paginationProps.current || 1,
            pageSize: paginationProps.pageSize || 20,
        };
        setPagination(newPagination);
        
        const params = {
            ...getSearchParams(),
            current: newPagination.current,
            pageSize: newPagination.pageSize,
        };
        
        loadData(params);
    };

    // 刷新统计数据
    const refreshRpmStats = async () => {
        if (lastParams) {
            isAdmin(userState) ? await getLogStat(lastParams, true) : await getLogSelfStat(lastParams, true);
        }
    };

    // 查看总额处理
    const handleViewTotalQuota = async () => {
        if (loadingTotalQuota) return;

        setLoadingTotalQuota(true);
        const loadingMessage = AntdMessage.loading({
            content: t('logsTable.loadingTotalQuota'),
            duration: 0,
        });

        try {
            let queryParams = {...lastParams};
            if (startTime && endTime) {
                queryParams.start_timestamp = startTime.unix();
                queryParams.end_timestamp = endTime.unix();
            }
            
            queryParams.use_redis = false;
            
            const url = isAdmin(userState) ? '/api/log/stat' : '/api/log/self/stat';
            const res = await API.get(url, {
                params: queryParams
            });

            if (res.data.success) {
                const totalStats = res.data.data;
                Modal.info({
                    title: t('logsTable.totalQuotaTitle'),
                    maskClosable: true,
                    content: (
                        <>
                            <p>{t('logsTable.totalQuota', {quota: renderQuota(totalStats.quota)})}</p>
                            <p>{t('logsTable.totalRpm', {rpm: totalStats.rpm})}</p>
                            <p>{t('logsTable.totalTpm', {tpm: totalStats.tpm})}</p>
                            <p>{t('logsTable.totalMpm', {mpm: totalStats.mpm.toFixed(4)})}</p>
                            <p>{t('logsTable.dailyEstimate', {estimate: renderQuota(totalStats.mpm * 60 * 24 * 500000)})}</p>
                        </>
                    ) as ReactNode,
                });
            } else {
                AntdMessage.error(t('logsTable.loadTotalQuotaError'));
            }
        } catch (error) {
            AntdMessage.error(t('logsTable.loadTotalQuotaError'));
        } finally {
            loadingMessage();
            setLoadingTotalQuota(false);
        }
    };

    // 处理自动刷新间隔变化
    const handleChangeAutoRefresh = (interval: number) => {
        setAutoRefreshInterval(interval);
        if (refreshTimer) {
            clearInterval(refreshTimer);
            setRefreshTimer(null);
        }
        if (interval > 0) {
            const timer = setInterval(refreshRpmStats, interval * 1000);
            setRefreshTimer(timer);
        }
    };

    // 打开日志详情弹窗
    const handelOpenLogDetailDrawer = (logId: number) => {
        if (!logId || logId === 0) {
            AntdMessage.error(t('logsTable.logIdNotExist'));
            return;
        }
        setShowLogDetailDrawerLogId(logId);
        setIsLogDetailDrawerVisible(true);
    };

    // 处理请求ID点击
    const handleRequestIdClick = (requestId: string) => {
        if (!isAdmin(userState)) {
            return;
        }
        setCurrentRequestId(requestId);
        setIsRequestLogsModalVisible(true);
        fetchRequestLogs(requestId);
    };

    // 获取请求日志
    const fetchRequestLogs = async (requestId: string) => {
        setLoadingRequestLogs(true);
        try {
            let url: string;
            if (isAdmin(userState)) {
                url = `/api/log/?p=0&pageSize=100&request_id=${requestId}`;
            } else {
                url = `/api/log/self/?p=0&pageSize=100&request_id=${requestId}`;
            }
            const res = await API.get(url);
            const {success, data} = res.data;
            if (success) {
                setRequestLogs(data);
            } else {
                showError(t('logsTable.failedToLoadRequestLogs'));
            }
        } catch (error) {
            showError(error);
        } finally {
            setLoadingRequestLogs(false);
        }
    };

    // 下载CSV
    const downloadCSV = (event) => {
        event.stopPropagation();
        if (!logs.length) {
            AntdMessage.info(t('logsTable.noDataToExport'));
            return;
        }
        const filteredData = logs.map((row: any) => {
            let rowObj = {
                [t('logsTable.usageTime')]: timestamp2string(row.created_at),
                [t('logsTable.tokenName')]: row.token_name,
                [t('logsTable.modelName')]: row.model_name,
                [t('logsTable.prompt')]: row.prompt_tokens,
                [t('logsTable.completion')]: row.completion_tokens,
                [t('logsTable.quotaConsumption')]: `$${(row.quota / 500000).toFixed(6)}`,
                [t('logsTable.timeConsumption')]: `${row.request_duration} ${t('logsTable.seconds')}`
            };
            if (isAdmin(userState)) {
                rowObj[t('logsTable.user')] = row.username;
                rowObj[t('logsTable.channel')] = row.channel;
                rowObj[t('logsTable.channelName')] = row.channel_name;
                rowObj[t('logsTable.description')] = row.content;
            }
            return rowObj;
        });

        const csv = Papa.unparse(filteredData);
        const blob = new Blob([csv], {type: 'text/csv'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'logs.csv';
        a.click();
        AntdMessage.success(t('logsTable.exportSuccess'));
    };

    // 管理员列定义
    const getAdminColumns = () => [
        {
            title: t('logsTable.time'),
            width: 180,
            dataIndex: 'created_at',
            key: 'created_at',
            sorter: true,
            render: (_text: any, record: any) => {
                let date = dayjs(record.created_at * 1000);
                if (date.year() === 1970) {
                    return '';
                } else {
                    return (
                        <Popover
                            title={t('logsTable.moreInfo')}
                            content={[
                                <>
                                    <p>{t('logsTable.time')}: {date.format('YYYY/MM/DD HH:mm:ss')}</p>
                                    <p>{t('logsTable.ip')}: {record.ip}</p>
                                    <p>
                                        {t('logsTable.requestId')}:
                                        <Button
                                            type="link"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleRequestIdClick(record.request_id);
                                            }}
                                            style={{padding: '0 4px'}}
                                        >
                                            {record.request_id}
                                        </Button>
                                    </p>
                                    <p>{t('logsTable.username')}: {record.username}</p>
                                    <p>{t('logsTable.userId')}: {record.user_id}</p>
                                    <p>{t('logsTable.tokenName')}: {record.token_name}</p>
                                    <p>{t('logsTable.token')}: sk-{record.token_key}</p>
                                </>,
                            ]}>
                            {timestamp2string(record.created_at)}
                        </Popover>
                    );
                }
            },
        },
        {
            title: t('logsTable.type'),
            dataIndex: 'type',
            width: 100,
            key: 'type',
            render: (_, record) => {
                const status = LOGS_TYPE[record.type];
                return status ? (
                    <Tag color={status.color}>
                        {t(`logsTable.type${status.text}`)}
                    </Tag>
                ) : (
                    <Tag color="default">{t('logsTable.typeUnknown')}</Tag>
                );
            }
        },
        {
            title: t('logsTable.channel'),
            width: 180,
            key: 'channel_info',
            render: (_channel_name, record) => {
                if (!record.channel_name || !record.channel) return '';
                return <Tag icon={<ControlFilled/>} color='default'
                            bordered={false}>{`${record.channel_name}（#${record.channel}）`}</Tag>;
            }
        },
        {
            title: t('logsTable.username'),
            width: 140,
            dataIndex: 'username',
            key: 'username',
            render: (username) => {
                if (!username) return '';
                return (
                    <Tooltip title={`${username} (${t('message.clipboard.clickToCopy')})`}>
                        <Tag
                            icon={<IdcardFilled/>}
                            color='purple'
                            bordered={false}
                            onClick={() => {
                                navigator.clipboard.writeText(username);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{cursor: 'pointer'}}
                        >
                            {username}
                        </Tag>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.token'),
            width: 140,
            dataIndex: 'token_name',
            key: 'token_name',
            ellipsis: true,
            render: (token_name, record) => {
                if (!token_name) return '';
                const displayName = typeof record.token_name === 'string' ? record.token_name : String(record.token_name);
                return (
                    <Tooltip title={`${displayName} (${t('message.clipboard.clickToCopy')})`}>
                        <Tag
                            icon={<LockFilled/>}
                            color='cyan'
                            bordered={false}
                            onClick={() => {
                                navigator.clipboard.writeText(displayName);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{
                                cursor: 'pointer',
                                maxWidth: '100%',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                            }}
                        >
                            {displayName}
                        </Tag>
                    </Tooltip>
                );
            }
        },
        {
            title: t('logsTable.tokenGroup'),
            width: 200,
            dataIndex: 'token_group',
            key: 'token_group',
            render: (token_group, record) => {
                if (!token_group) return '';

                if (groupOptions.length === 0) {
                    return (
                        <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                            <span style={{flex: 1, overflow: 'hidden', textOverflow: 'ellipsis'}}>{token_group}</span>
                            {loading && <LoadingOutlined style={{fontSize: 14}}/>}
                        </div>
                    );
                }

                const group = groupOptions.find(g => g.name === token_group);
                if (!group) return token_group;
                const isCurrentGroup = group.name === userState.user.group;
                const ratio = isCurrentGroup ? 
                    group.current_group_ratio : 
                    (group.convert_ratio || 1) * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);

                const maxDecimalPlaces = Math.max(
                    getNumberDecimalPlaces(group.convert_ratio || 1),
                    getNumberDecimalPlaces(group.current_group_ratio || 1),
                    getNumberDecimalPlaces(group.current_topup_ratio || 1)
                );

                const formattedRatio = ratio.toFixed(maxDecimalPlaces);

                return (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line'}}>
                            <p><strong>分组名称:</strong> {group.display_name}</p>
                            <p><strong>分组标识:</strong> {group.name}</p>
                            <p><strong>转换倍率:</strong> {group.convert_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>分组倍率:</strong> {group.current_group_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>充值倍率:</strong> {group.current_topup_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>最终倍率:</strong> {formattedRatio}x</p>
                        </div>
                    }>
                        <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                            gap: '8px'
                        }}>
                            <span style={{
                                flex: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                minWidth: 0
                            }}>
                                {group.display_name || token_group}
                            </span>
                            <span style={{
                                padding: '2px 8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                backgroundColor: Number(formattedRatio) > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                color: Number(formattedRatio) > 1 ? '#ff4d4f' : '#52c41a',
                                flexShrink: 0
                            }}>
                                {formattedRatio}x
                            </span>
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.model'),
            width: 170,
            dataIndex: 'model_name',
            key: 'model_name',
            render: (model_name) => {
                if (!model_name) return '';
                return (
                    <Tooltip title={`${model_name} (${t('message.clipboard.clickToCopy')})`}>
                        <div
                            onClick={() => {
                                navigator.clipboard.writeText(model_name as string);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{cursor: 'pointer'}}
                        >
                            {renderModelTags(model_name as string)}
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.info'),
            width: 100,
            key: 'request_info',
            render: (_, record) => {
                return (
                    <>
                        {renderIsStream(record)}
                        {renderTotalDuration(record, durationType)}
                    </>
                )
            }
        },
        {
            title: t('logsTable.prompt'),
            width: 80,
            dataIndex: 'prompt_tokens',
            key: 'prompt_tokens',
            sorter: true,
            render: (_, record) => record.prompt_tokens === 0 ? '' : record.prompt_tokens,
        },
        {
            title: t('logsTable.completion'),
            width: 80,
            dataIndex: 'completion_tokens',
            key: 'completion_tokens',
            sorter: true,
            render: (_, record) => record.completion_tokens === 0 ? '' : record.completion_tokens,
        },
        {
            title: t('logsTable.consumption'),
            width: 120,
            dataIndex: 'quota',
            key: 'quota',
            sorter: true,
            render: (_, record) => {
                if (record.quota === 0 || typeof record.quota === 'undefined' || record.quota === null) {
                    return '';
                }

                const revenue = record.quota / 500000;
                const cost = record.cost_quota ? record.cost_quota / 500000 : 0;
                const profit = revenue - cost;

                return (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line'}}>
                            {`收入: $${revenue.toFixed(6)}
成本: $${cost.toFixed(6)}
利润: $${profit.toFixed(6)}`}
                        </div>
                    }>
                        <span style={{cursor: 'pointer'}}>
                            ${revenue.toFixed(6)}
                        </span>
                    </Tooltip>
                );
            }
        },
        {
            title: t('logsTable.retry'),
            width: 80,
            key: 'action-retry',
            render: (_, record) => {
                if (!record.other) return '';
                let otherObj;
                try {
                    otherObj = JSON.parse(record.other);
                } catch (e) {
                    console.error('Failed to parse other field:', e);
                    return '';
                }
                const admin_info = otherObj?.admin_info;
                if (!admin_info) return '';
                const use_channel = admin_info.use_channel;
                const retry_durations = admin_info.retry_durations;
                if (!use_channel || use_channel.length === 0) return '';

                return (
                    <Popover
                        title={t('logsTable.retryChannelList')}
                        content={
                            <Space direction="vertical">
                                <div>
                                    {use_channel.map((channel: number, index: number) => (
                                        <React.Fragment key={channel}>
                                            <Tag color='blue'>{channel}</Tag>
                                            {index < use_channel.length - 1 && (
                                                <span style={{margin: '0 5px'}}>→</span>
                                            )}
                                        </React.Fragment>
                                    ))}
                                </div>
                                {retry_durations && retry_durations.length > 0 && (
                                    <div style={{marginTop: '8px'}}>
                                        <Typography.Text type="secondary">
                                            {t('logsTable.retryDurations')}:
                                        </Typography.Text>
                                        {retry_durations.map((retry: any, index: number) => (
                                            <div key={index} style={{marginTop: '4px'}}>
                                                <Tag color="cyan">
                                                    {t('logsTable.channel')}: {retry.channel}
                                                </Tag>
                                                <Tag color="green">
                                                    {t('logsTable.duration')}: {retry.duration}s
                                                </Tag>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </Space>
                        }
                    >
                        <Button type='link' size="small">
                            {t('logsTable.retry')}
                            {retry_durations && retry_durations.length > 0 && (
                                <sup style={{color: '#ff4d4f'}}>
                                    {retry_durations.length}
                                </sup>
                            )}
                        </Button>
                    </Popover>
                );
            },
        },
        {
            title: t('logsTable.ip'),
            width: 140,
            dataIndex: 'ip',
            key: 'ip',
            ellipsis: true,
            render: (ip: any, record: any) => {
                const ipValue = record.ip;
                if (!ipValue || typeof ipValue !== 'string') return '';

                // 分类收集所有有值的IP信息
                const ipCategories: { [key: string]: string[] } = {
                    'primary': [],
                    'proxy': [],
                    'cdn': []
                };

                // 主要IP信息
                if (record.ip) ipCategories.primary.push(`${t('logsTable.ip')}: ${record.ip}`);
                if (record.remote_ip && record.remote_ip !== record.ip) {
                    ipCategories.primary.push(`${t('logsTable.remoteIp')}: ${record.remote_ip}`);
                }

                // 代理相关IP
                if (record.x_forwarded_for) {
                    ipCategories.proxy.push(`${t('logsTable.xForwardedFor')}: ${record.x_forwarded_for}`);
                }
                if (record.x_real_ip) {
                    ipCategories.proxy.push(`${t('logsTable.xRealIp')}: ${record.x_real_ip}`);
                }

                // CDN相关IP
                if (record.cf_connecting_ip) {
                    ipCategories.cdn.push(`${t('logsTable.cfConnectingIp')}: ${record.cf_connecting_ip}`);
                }

                // 构建分类显示的tooltip内容
                const tooltipSections: string[] = [];
                if (ipCategories.primary.length > 0) {
                    tooltipSections.push(`📍 ${t('logsTable.primaryIp')}:\n${ipCategories.primary.join('\n')}`);
                }
                if (ipCategories.proxy.length > 0) {
                    tooltipSections.push(`🔄 ${t('logsTable.proxyIp')}:\n${ipCategories.proxy.join('\n')}`);
                }
                if (ipCategories.cdn.length > 0) {
                    tooltipSections.push(`☁️ ${t('logsTable.cdnIp')}:\n${ipCategories.cdn.join('\n')}`);
                }

                // 计算总的IP信息数量
                const totalIpCount = ipCategories.primary.length + ipCategories.proxy.length + ipCategories.cdn.length;

                const ipContent = totalIpCount > 1 ? (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line', maxWidth: '300px'}}>
                            {tooltipSections.join('\n\n')}
                        </div>
                    }>
                        <span style={{borderBottom: '1px dashed #ccc', cursor: 'help'}}>
                            {ipValue} {totalIpCount > 1 && <span style={{fontSize: '12px', color: '#999'}}>({totalIpCount})</span>}
                        </span>
                    </Tooltip>
                ) : (
                    <span>{ipValue}</span>
                );

                return isInternalIP(ipValue) ? (
                    ipContent
                ) : (
                    <a
                        href={`https://ip.sb/ip/${ipValue}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{color: '#1890ff'}}
                    >
                        {ipContent}
                    </a>
                );
            },
        },
        {
            title: t('logsTable.description'),
            width: 150,
            dataIndex: 'content',
            key: 'content',
            ellipsis: true,
            render: (text, record) => {
                let otherObj;
                try {
                    otherObj = record.other ? JSON.parse(record.other) : null;
                } catch (e) {
                    console.error('Failed to parse other field:', e);
                }

                // 如果有错误码，显示带错误码的 Tooltip
                if (record.error_code) {
                    return (
                        <Tooltip
                            title={
                                <div style={{whiteSpace: 'pre-line'}}>
                                    {`${record.content}\n${t('logsTable.errorCode')}: ${record.error_code}`}
                                </div>
                            }
                            mouseEnterDelay={0}
                            placement="left"
                        >
                            <span
                                style={{cursor: 'pointer', color: '#ff4d4f'}}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setCurrentContent(record.content);
                                    setIsModalVisible(true);
                                }}
                            >
                                {record.content}
                            </span>
                        </Tooltip>
                    );
                }

                // 如果有倍率信息，保持原有的 Popover 逻辑
                if (otherObj && otherObj.model_ratio) {
                    const {model_ratio, completion_ratio, group_ratio, topup_convert_ratio, user_discount} = otherObj;
                    const ratio = model_ratio * group_ratio * user_discount;
                    const quota = Math.ceil((record.prompt_tokens + record.completion_tokens * completion_ratio) * ratio * topup_convert_ratio);

                    return (
                        <Popover
                            content={
                                <div style={{fontSize: '14px', lineHeight: '1.6'}}>
                                    <p><strong>提示倍率:</strong> {model_ratio}</p>
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>对应官方价格:</strong> ${(model_ratio * 2).toFixed(2)} / 1M input tokens
                                    </p>
                                    <p><strong>补全倍率:</strong> {completion_ratio}</p>
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>对应官方价格:</strong> ${(completion_ratio * model_ratio * 2).toFixed(2)} /
                                        1M output tokens</p>
                                    <p><strong>提示 tokens:</strong> {record.prompt_tokens}</p>
                                    <p><strong>补全 tokens:</strong> {record.completion_tokens}</p>
                                    <p><strong>分组倍率:</strong> {group_ratio}</p>
                                    <p><strong>充值转换率:</strong> {topup_convert_ratio}</p>
                                    <p><strong>用户折扣率:</strong> {user_discount}</p>
                                    <p>
                                        <strong>计算过程:</strong><br/>
                                        ({record.prompt_tokens} + {record.completion_tokens} * {completion_ratio})
                                        * {model_ratio} * {group_ratio} * {topup_convert_ratio} * {user_discount} /
                                        500000 = ${(quota / 500000).toFixed(6)}
                                    </p>
                                    <p><strong>扣费金额:</strong> ${(quota / 500000).toFixed(6)}</p>
                                    <p style={{color: '#999'}}>仅供参考,以实际扣费为准</p>
                                </div>
                            }
                            placement="left"
                            trigger="hover"
                        >
                            <span>{record.content}</span>
                        </Popover>
                    );
                } else {
                    // 没有倍率信息时，显示 content 字段内容
                    return (
                        <Tooltip
                            title={record.content}
                            mouseEnterDelay={0}
                            placement="left"
                        >
                            <span
                                style={{cursor: 'pointer', color: '#1890ff'}}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setCurrentContent(record.content);
                                    setIsModalVisible(true);
                                }}
                            >
                                {record.content}
                            </span>
                        </Tooltip>
                    );
                }
            },
        },
        {
            title: t('logsTable.action'),
            width: 70,
            align: 'center' as const,
            key: 'action-detail',
            fixed: 'right' as const,
            render: (_, record) => {
                return <Button type='link' size="small"
                               onClick={() => handelOpenLogDetailDrawer(record.id ?? 0)}>{t('logsTable.details')}</Button>;
            },
        }
    ];

    // 用户列定义
    const getUserColumns = () => [
        {
            title: t('logsTable.time'),
            width: 180,
            dataIndex: 'created_at',
            key: 'created_at',
            sorter: true,
            render: (_, record) => {
                let date = dayjs(record.created_at * 1000);
                return (
                    <Tooltip title={t('message.clipboard.clickToCopy')}>
                        <span style={{cursor: 'pointer'}} onClick={() => {
                            navigator.clipboard.writeText(date.format('YYYY/MM/DD HH:mm:ss'));
                            AntdMessage.success(t('message.clipboard.copySuccess'));
                        }}>
                            {timestamp2string(record.created_at)}
                        </span>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.type'),
            dataIndex: 'type',
            width: 80,
            key: 'type',
            render: (_, record) => {
                const status = LOGS_TYPE[record.type];
                return <Tag color={status.color}> {t(`logsTable.type${status.text}`)} </Tag>;
            }
        },
        {
            title: t('logsTable.tokenName'),
            width: 200,
            dataIndex: 'token_name',
            key: 'token_name',
            render: (token_name) => {
                if (!token_name) return '';
                const displayName = typeof token_name === 'string' ? token_name : String(token_name);
                return (
                    <Tooltip title={`${displayName} (${t('message.clipboard.clickToCopy')})`}>
                        <Tag
                            icon={<LockFilled/>}
                            color='cyan'
                            bordered={false}
                            style={{cursor: 'pointer'}}
                            onClick={() => {
                                navigator.clipboard.writeText(displayName);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                        >
                            {displayName}
                        </Tag>
                    </Tooltip>
                );
            }
        },
        {
            title: t('logsTable.tokenGroup'),
            width: 200,
            dataIndex: 'token_group',
            key: 'token_group',
            render: (token_group, record) => {
                if (!token_group) return '';

                if (groupOptions.length === 0) {
                    return (
                        <div style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                            <span>{token_group}</span>
                            {loading && <LoadingOutlined style={{fontSize: 14}}/>}
                        </div>
                    );
                }

                const group = groupOptions.find(g => g.name === token_group);
                if (!group) return token_group;
                const isCurrentGroup = group.name === userState.user.group;
                const ratio = isCurrentGroup ? 
                    group.current_group_ratio : 
                    (group.convert_ratio || 1) * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);

                const maxDecimalPlaces = Math.max(
                    getNumberDecimalPlaces(group.convert_ratio || 1),
                    getNumberDecimalPlaces(group.current_group_ratio || 1),
                    getNumberDecimalPlaces(group.current_topup_ratio || 1)
                );

                const formattedRatio = ratio.toFixed(maxDecimalPlaces);

                return (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line'}}>
                            <p><strong>分组名称:</strong> {group.display_name}</p>
                            <p><strong>分组标识:</strong> {group.name}</p>
                            <p><strong>转换倍率:</strong> {group.convert_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>分组倍率:</strong> {group.current_group_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>充值倍率:</strong> {group.current_topup_ratio?.toFixed(1) || '1.0'}</p>
                            <p><strong>最终倍率:</strong> {formattedRatio}x</p>
                        </div>
                    }>
                        <div style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            width: '100%',
                            gap: '8px'
                        }}>
                            <span style={{
                                flex: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                minWidth: 0
                            }}>
                                {group.display_name || token_group}
                            </span>
                            <span style={{
                                padding: '2px 8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                backgroundColor: Number(formattedRatio) > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                color: Number(formattedRatio) > 1 ? '#ff4d4f' : '#52c41a',
                                flexShrink: 0
                            }}>
                                {formattedRatio}x
                            </span>
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.model'),
            width: 200,
            dataIndex: 'model_name',
            key: 'model_name',
            render: (model_name) => {
                if (!model_name) return '';
                return (
                    <Tooltip title={`${model_name} (${t('message.clipboard.clickToCopy')})`}>
                        <div
                            onClick={() => {
                                navigator.clipboard.writeText(model_name as string);
                                AntdMessage.success(t('message.clipboard.copySuccess'));
                            }}
                            style={{cursor: 'pointer'}}
                        >
                            {renderModelTags(model_name as string)}
                        </div>
                    </Tooltip>
                );
            },
        },
        {
            title: t('logsTable.duration'),
            width: 90,
            dataIndex: 'duration_for_view',
            key: 'duration_for_view',
            render: (text, record) => {
                if (record.type === 2) {
                    if (record.duration_for_view < 1) {
                        return <Tag icon={<ClockCircleFilled/>} bordered={false}
                                    color='success'>{t('logsTable.lessThanOneSecond')}</Tag>;
                    } else {
                        return <Tag icon={<ClockCircleFilled/>} bordered={false}
                                    color='success'>{text}{t('logsTable.seconds')}</Tag>;
                    }
                } else {
                    return '';
                }
            }
        },
        {
            title: t('logsTable.isStream'),
            width: 80,
            dataIndex: 'is_stream',
            key: 'is_stream',
            render: (_, record) => renderIsStream(record),
        },
        {
            title: t('logsTable.prompt'),
            width: 80,
            dataIndex: 'prompt_tokens',
            key: 'prompt_tokens',
            sorter: true,
            render: (text) => text === 0 ? '' : text
        },
        {
            title: t('logsTable.completion'),
            width: 80,
            dataIndex: 'completion_tokens',
            key: 'completion_tokens',
            sorter: true,
            render: (text) => text === 0 ? '' : text
        },
        {
            title: t('logsTable.consumption'),
            width: 120,
            dataIndex: 'quota',
            key: 'quota',
            sorter: true,
            render: (_, record) => (record.quota === 0 || typeof record.quota === 'undefined' || record.quota === null) ? '' : `$${(record.quota / 500000).toFixed(6)}`
        },
        {
            title: t('logsTable.ip'),
            width: 140,
            dataIndex: 'ip',
            key: 'ip',
            ellipsis: true,
            render: (ip: any, record: any) => {
                const ipValue = record.ip;
                if (!ipValue || typeof ipValue !== 'string') return '';

                // 分类收集所有有值的IP信息
                const ipCategories: { [key: string]: string[] } = {
                    'primary': [],
                    'proxy': [],
                    'cdn': []
                };

                // 主要IP信息
                if (record.ip) ipCategories.primary.push(`${t('logsTable.ip')}: ${record.ip}`);
                if (record.remote_ip && record.remote_ip !== record.ip) {
                    ipCategories.primary.push(`${t('logsTable.remoteIp')}: ${record.remote_ip}`);
                }

                // 代理相关IP
                if (record.x_forwarded_for) {
                    ipCategories.proxy.push(`${t('logsTable.xForwardedFor')}: ${record.x_forwarded_for}`);
                }
                if (record.x_real_ip) {
                    ipCategories.proxy.push(`${t('logsTable.xRealIp')}: ${record.x_real_ip}`);
                }

                // CDN相关IP
                if (record.cf_connecting_ip) {
                    ipCategories.cdn.push(`${t('logsTable.cfConnectingIp')}: ${record.cf_connecting_ip}`);
                }

                // 构建分类显示的tooltip内容
                const tooltipSections: string[] = [];
                if (ipCategories.primary.length > 0) {
                    tooltipSections.push(`📍 ${t('logsTable.primaryIp')}:\n${ipCategories.primary.join('\n')}`);
                }
                if (ipCategories.proxy.length > 0) {
                    tooltipSections.push(`🔄 ${t('logsTable.proxyIp')}:\n${ipCategories.proxy.join('\n')}`);
                }
                if (ipCategories.cdn.length > 0) {
                    tooltipSections.push(`☁️ ${t('logsTable.cdnIp')}:\n${ipCategories.cdn.join('\n')}`);
                }

                // 计算总的IP信息数量
                const totalIpCount = ipCategories.primary.length + ipCategories.proxy.length + ipCategories.cdn.length;

                const ipContent = totalIpCount > 1 ? (
                    <Tooltip title={
                        <div style={{whiteSpace: 'pre-line', maxWidth: '300px'}}>
                            {tooltipSections.join('\n\n')}
                        </div>
                    }>
                        <span style={{borderBottom: '1px dashed #ccc', cursor: 'help'}}>
                            {ipValue} {totalIpCount > 1 && <span style={{fontSize: '12px', color: '#999'}}>({totalIpCount})</span>}
                        </span>
                    </Tooltip>
                ) : (
                    <span>{ipValue}</span>
                );

                return isInternalIP(ipValue) ? (
                    ipContent
                ) : (
                    <a
                        href={`https://ip.sb/ip/${ipValue}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{color: '#1890ff'}}
                    >
                        {ipContent}
                    </a>
                );
            },
        },
        {
            title: t('logsTable.description'),
            width: 150,
            dataIndex: 'content',
            key: 'content',
            ellipsis: true,
            render: (text, record) => {
                if (!statusState.status.UserLogViewEnabled && record.type === 2) {
                    return t('logsTable.modelInvocation');
                }

                let otherObj;
                try {
                    otherObj = record.other ? JSON.parse(record.other) : null;
                } catch (e) {
                    console.error('Failed to parse other field:', e);
                }

                // 如果有倍率信息，保持原有的 Popover 逻辑
                if (otherObj && otherObj.model_ratio) {
                    const {model_ratio, completion_ratio, group_ratio, topup_convert_ratio, user_discount} = otherObj;
                    const ratio = model_ratio * group_ratio * user_discount;
                    const quota = Math.ceil((record.prompt_tokens + record.completion_tokens * completion_ratio) * ratio * topup_convert_ratio);

                    // 检查是否有cache相关信息
                    const cacheBilling = otherObj?.cache_billing;
                    const hasCacheInfo = cacheBilling && cacheBilling.cache_enabled;

                    return (
                        <Popover
                            content={
                                <div style={{fontSize: '14px', lineHeight: '1.6'}}>
                                    <p><strong>提示倍率:</strong> {model_ratio}</p>
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>对应官方价格:</strong> ${(model_ratio * 2).toFixed(2)} / 1M input tokens
                                    </p>
                                    <p><strong>补全倍率:</strong> {completion_ratio}</p>
                                    <p style={{marginLeft: '20px'}}>
                                        <strong>对应官方价格:</strong> ${(completion_ratio * model_ratio * 2).toFixed(2)} /
                                        1M output tokens</p>
                                    <p><strong>提示 tokens:</strong> {record.prompt_tokens}</p>
                                    <p><strong>补全 tokens:</strong> {record.completion_tokens}</p>
                                    <p><strong>分组倍率:</strong> {group_ratio}</p>
                                    <p><strong>充值转换率:</strong> {topup_convert_ratio}</p>
                                    <p><strong>用户折扣率:</strong> {user_discount}</p>

                                    {/* 如果有cache信息，显示cache相关的详细计费 */}
                                    {hasCacheInfo && (
                                        <div style={{marginTop: '12px', padding: '8px', backgroundColor: '#f0f8ff', borderRadius: '4px', border: '1px solid #d1ecf1'}}>
                                            <p><strong>🗄️ 缓存计费详情</strong></p>
                                            {cacheBilling.cache_creation_tokens > 0 && (
                                                <p style={{marginLeft: '20px'}}>
                                                    <strong>缓存创建:</strong> {cacheBilling.cache_creation_tokens} tokens × {cacheBilling.cache_creation_ratio}x = {cacheBilling.cache_creation_cost.toFixed(2)}
                                                </p>
                                            )}
                                            {cacheBilling.cache_read_tokens > 0 && (
                                                <p style={{marginLeft: '20px'}}>
                                                    <strong>缓存读取:</strong> {cacheBilling.cache_read_tokens} tokens × {cacheBilling.cache_read_ratio}x = {cacheBilling.cache_read_cost.toFixed(2)}
                                                </p>
                                            )}
                                            <p style={{marginLeft: '20px'}}>
                                                <strong>缓存总费用:</strong> {cacheBilling.total_cache_cost.toFixed(2)} tokens
                                            </p>
                                            <p style={{marginLeft: '20px', color: '#666', fontSize: '12px'}}>
                                                缓存创建费率高(1.25x)，读取费率低(0.1x)，重复使用可节省费用
                                            </p>
                                        </div>
                                    )}

                                    <p>
                                        <strong>计算过程:</strong><br/>
                                        {hasCacheInfo ? (
                                            <>
                                                基础费用: ({record.prompt_tokens} + {record.completion_tokens} * {completion_ratio}) * {model_ratio} * {group_ratio} * {topup_convert_ratio} * {user_discount}<br/>
                                                缓存费用: {cacheBilling.total_cache_cost.toFixed(2)} * {model_ratio} * {group_ratio} * {topup_convert_ratio} * {user_discount}<br/>
                                                总计: ${(record.quota / 500000).toFixed(6)}
                                            </>
                                        ) : (
                                            <>
                                                ({record.prompt_tokens} + {record.completion_tokens} * {completion_ratio})
                                                * {model_ratio} * {group_ratio} * {topup_convert_ratio} * {user_discount} /
                                                500000 = ${(quota / 500000).toFixed(6)}
                                            </>
                                        )}
                                    </p>
                                    <p><strong>扣费金额:</strong> ${(record.quota / 500000).toFixed(6)}</p>
                                    <p style={{color: '#999'}}>仅供参考,以实际扣费为准</p>
                                </div>
                            }
                            placement="left"
                            trigger="hover"
                        >
                            <span>{record.content}</span>
                        </Popover>
                    );
                } else {
                    // 没有倍率信息时，显示 content 字段内容
                    return (
                        <Tooltip
                            title={record.content}
                            mouseEnterDelay={0}
                            placement="left"
                        >
                            <span
                                style={{cursor: 'pointer', color: '#1890ff'}}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setCurrentContent(record.content);
                                    setIsModalVisible(true);
                                }}
                            >
                                {record.content}
                            </span>
                        </Tooltip>
                    );
                }
            },
        }
    ];

    // 获取表格列（包含拖拽和显示/隐藏功能）
    const getColumns = useMemo((): ColumnsType<any> => {
        const baseColumns = isAdmin(userState) ? getAdminColumns() : getUserColumns();
        
        return baseColumns
            .filter(col => {
                const key = col.key || col.dataIndex;
                return key ? visibleColumns[key] !== false : true;
            })
            .map((col, index) => {
                const key = col.key || col.dataIndex;
                const width = key && columnWidths[key] ? columnWidths[key] : col.width;
                
                return {
                    ...col,
                    width,
                    onHeaderCell: (column: any) => ({
                        width,
                        onResize: handleResize(index),
                    } as any),
                };
            });
    }, [isAdmin(userState), visibleColumns, columnWidths, durationType, groupOptions, t]);

    // 处理切换持续时间类型
    const handleChangeLogDurationType = (value: string[]) => {
        if (value.length === 0) {
            return;
        }
        setDurationType(value[0]);
    };

    // 用户设置组件
    const UserSettingsComponent = () => (
        <Space style={{ marginLeft: 16 }}>
            <Tooltip title="设置默认查询范围，下次访问自动应用">
                <Switch 
                    checkedChildren="当日"
                    unCheckedChildren="全部" 
                    checked={userOnlyToday}
                    onChange={(checked) => {
                        setUserOnlyToday(checked);
                        AntdMessage.success(`已设置默认查询范围为${checked ? '当日' : '全部'}，下次访问将自动应用此设置`);
                    }}
                    style={{ transform: 'scale(1.1)' }}
                />
            </Tooltip>
            
            <Tooltip title="设置默认时间单位，下次访问自动应用">
                <Select 
                    value={userTimeUnit}
                    onChange={(value) => {
                        setUserTimeUnit(value);
                        AntdMessage.success(`已设置默认时间单位为${value === 'hour' ? '小时' : value === 'day' ? '天' : value === 'week' ? '周' : '月'}，下次访问将自动应用此设置`);
                    }}
                    style={{ width: 80, height: 32 }}
                    options={[
                        { value: 'hour', label: '小时' },
                        { value: 'day', label: '天' },
                        { value: 'week', label: '周' },
                        { value: 'month', label: '月' }
                    ]}
                    dropdownMatchSelectWidth={false}
                />
            </Tooltip>
        </Space>
    );

    // 搜索表单组件
    const SearchForm = () => (
        <Form
            form={searchForm}
            layout="vertical"
            onFinish={handleSearch}
            style={{ padding: '16px', maxHeight: '70vh', overflowY: 'auto' }}
        >
            {/* 时间选择 */}
            <Form.Item label={t('logsTable.time')} name="created_time">
                <RangePicker
                    showTime
                    style={{ width: '100%' }}
                    ranges={{
                        [t('logsTable.today')]: [dayjs().startOf('day'), dayjs().endOf('day')],
                        [t('logsTable.lastHour')]: [dayjs().subtract(1, 'hours'), dayjs()],
                        [t('logsTable.last3Hours')]: [dayjs().subtract(3, 'hours'), dayjs()],
                        [t('logsTable.lastDay')]: [dayjs().subtract(1, 'days'), dayjs()],
                        [t('logsTable.last3Days')]: [dayjs().subtract(3, 'days'), dayjs()],
                        [t('logsTable.last7Days')]: [dayjs().subtract(7, 'days'), dayjs()],
                        [t('logsTable.lastMonth')]: [dayjs().subtract(1, 'months'), dayjs()],
                        [t('logsTable.last3Months')]: [dayjs().subtract(3, 'months'), dayjs()]
                    }}
                />
            </Form.Item>

            <Row gutter={16}>
                {/* 类型选择 */}
                <Col span={12}>
                    <Form.Item label={t('logsTable.type')} name="type">
                        <Select
                            mode="multiple"
                            placeholder={t('logsTable.selectType')}
                            options={LOGS_TYPE_OPTIONS.map(option => ({
                                value: option.value,
                                label: t(`logsTable.type${LOGS_TYPE[option.value].text}`)
                            }))}
                            allowClear
                        />
                    </Form.Item>
                </Col>

                {/* 流式选择 */}
                <Col span={12}>
                    <Form.Item label={t('logsTable.isStream')} name="is_stream">
                        <Select placeholder={t('logsTable.selectIsStream')} allowClear>
                            <Select.Option value="true">{t('common.yes')}</Select.Option>
                            <Select.Option value="false">{t('common.no')}</Select.Option>
                        </Select>
                    </Form.Item>
                </Col>
            </Row>

            {/* 管理员字段 */}
            {isAdmin(userState) && (
                <>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item label={t('logsTable.channelId')} name="channel">
                                <Input placeholder={t('logsTable.channelIdPlaceholder')} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label={t('logsTable.channelName')} name="channel_name">
                                <Input placeholder={t('logsTable.channelNamePlaceholder')} />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item label={t('logsTable.username')} name="username">
                                <Input placeholder={t('logsTable.usernamePlaceholder')} />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item label={t('logsTable.ip')} name="ip">
                                <Input placeholder={t('logsTable.ipPlaceholder')} />
                            </Form.Item>
                        </Col>
                    </Row>
                </>
            )}

            <Row gutter={16}>
                {/* Token名称 */}
                <Col span={12}>
                    <Form.Item label={t('logsTable.tokenName')} name="token_name">
                        <Input placeholder={t('logsTable.tokenNamePlaceholder')} />
                    </Form.Item>
                </Col>

                {/* 分组选择 */}
                <Col span={12}>
                    <Form.Item label={t('logsTable.tokenGroup')} name="token_group">
                        <Select<string>
                            placeholder={t('logsTable.selectGroup')}
                            allowClear={true}
                            showSearch={true}
                            style={{
                                width: '100%',
                                fontSize: '14px'
                            }}
                            dropdownStyle={{
                                padding: '8px',
                                maxHeight: '400px',
                                minWidth: '450px',
                                width: 'auto'
                            }}
                            optionLabelProp="label"
                            onSearch={setGroupSearchText}
                            filterOption={false}
                        >
                            {filteredGroupOptions.length > 0 ? (
                                filteredGroupOptions.map(group => {
                                    const isCurrentGroup = group.name === userState.user.group;
                                    const displayRatio = isCurrentGroup
                                        ? group.current_group_ratio
                                        : group.convert_ratio * (group.current_group_ratio || 1) * (group.current_topup_ratio || 1);

                                    const decimalPlaces = Math.max(
                                        getNumberDecimalPlaces(group.convert_ratio),
                                        getNumberDecimalPlaces(group.current_group_ratio || 1),
                                        getNumberDecimalPlaces(group.current_topup_ratio || 1)
                                    );

                                    return (
                                        <Select.Option
                                            key={group.id}
                                            value={group.name}
                                            label={group.display_name}
                                        >
                                            <div style={{
                                                padding: '8px 4px'
                                            }}>
                                                <div style={{
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center',
                                                    marginBottom: '4px'
                                                }}>
                                                    <span style={{
                                                        color: group.color_mapping,
                                                        fontWeight: 500
                                                    }}>
                                                        <HighlightText
                                                            text={group.display_name}
                                                            searchText={debouncedGroupSearchText}
                                                        />
                                                        {isCurrentGroup && (
                                                            <Tag color="blue" style={{marginLeft: 8}}>
                                                                {t('editTokenModal.currentGroup')}
                                                            </Tag>
                                                        )}
                                                    </span>
                                                    <span style={{
                                                        padding: '2px 8px',
                                                        borderRadius: '4px',
                                                        fontSize: '12px',
                                                        backgroundColor: displayRatio > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                                        color: displayRatio > 1 ? '#ff4d4f' : '#52c41a'
                                                    }}>
                                                        <HighlightText
                                                            text={`${displayRatio.toFixed(decimalPlaces)}x`}
                                                            searchText={debouncedGroupSearchText}
                                                        />
                                                    </span>
                                                </div>
                                                {group.description && (
                                                    <div style={{
                                                        position: 'relative',
                                                        fontSize: '12px',
                                                        color: 'rgba(0, 0, 0, 0.45)',
                                                        lineHeight: '1.5',
                                                        overflow: 'hidden',
                                                        height: expandedGroupOptions[group.name] ? 'auto' : '20px'
                                                    }}>
                                                        <div style={{
                                                            whiteSpace: expandedGroupOptions[group.name] ? 'normal' : 'nowrap',
                                                            textOverflow: 'ellipsis',
                                                            overflow: 'hidden'
                                                        }}>
                                                            <HighlightText
                                                                text={group.description}
                                                                searchText={debouncedGroupSearchText}
                                                            />
                                                        </div>
                                                        {group.description.length > 50 && (
                                                            <Button
                                                                type="link"
                                                                size="small"
                                                                onClick={(e) => toggleGroupExpand(group.name, e)}
                                                                style={{
                                                                    position: 'absolute',
                                                                    right: 0,
                                                                    bottom: 0,
                                                                    padding: '0 4px',
                                                                    background: '#fff'
                                                                }}
                                                            >
                                                                {expandedGroupOptions[group.name] ? t('common.collapse') : t('common.expand')}
                                                            </Button>
                                                        )}
                                                    </div>
                                                )}
                                                <div style={{
                                                    fontSize: '11px',
                                                    color: 'rgba(0, 0, 0, 0.25)',
                                                    marginTop: '4px'
                                                }}>
                                                    <HighlightText
                                                        text={`标识: ${group.name}`}
                                                        searchText={debouncedGroupSearchText}
                                                    />
                                                </div>
                                            </div>
                                        </Select.Option>
                                    );
                                })
                            ) : (
                                <Select.Option disabled value="">
                                    {t('common.loading')}
                                </Select.Option>
                            )}
                        </Select>
                    </Form.Item>
                </Col>
            </Row>

            <Row gutter={16}>
                {/* 模型名称 */}
                <Col span={12}>
                    <Form.Item label={t('logsTable.model')} name="model_name">
                        <AutoComplete
                            options={AUTOCOMPLETE_MODEL_NAMES.map(name => ({value: name}))}
                            placeholder={t('logsTable.modelPlaceholder')}
                            filterOption={(inputValue, option) => {
                                if (!option?.value) return false;
                                return option.value.toLowerCase().includes(inputValue.toLowerCase());
                            }}
                            allowClear
                        />
                    </Form.Item>
                </Col>

                {/* 排除模型 */}
                <Col span={12}>
                    <Form.Item label={t('logsTable.excludeModels')} name="exclude_models">
                        <Select
                            mode="tags"
                            placeholder="输入/选择模型名称"
                            options={AUTOCOMPLETE_MODEL_NAMES.map(name => ({value: name, label: name}))}
                            allowClear
                            showSearch
                            filterOption={(input, option) => {
                                if (typeof option?.label === 'string') {
                                    return option.label.toLowerCase().includes(input.toLowerCase());
                                }
                                return false;
                            }}
                        />
                    </Form.Item>
                </Col>
            </Row>

            {/* Token范围 */}
            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item label={t('logsTable.prompt') + " 范围"}>
                        <Input.Group compact>
                            <Form.Item name="prompt_tokens_min" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber placeholder="最小值" min={0} style={{ width: '100%' }} />
                            </Form.Item>
                            <Form.Item name="prompt_tokens_max" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber placeholder="最大值" min={0} style={{ width: '100%' }} />
                            </Form.Item>
                        </Input.Group>
                    </Form.Item>
                </Col>

                <Col span={12}>
                    <Form.Item label={t('logsTable.completion') + " 范围"}>
                        <Input.Group compact>
                            <Form.Item name="completion_tokens_min" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber placeholder="最小值" min={0} style={{ width: '100%' }} />
                            </Form.Item>
                            <Form.Item name="completion_tokens_max" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber placeholder="最大值" min={0} style={{ width: '100%' }} />
                            </Form.Item>
                        </Input.Group>
                    </Form.Item>
                </Col>
            </Row>

            {/* 消费范围 */}
            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item label={t('logsTable.consumption') + " 范围 ($)"}>
                        <Input.Group compact>
                            <Form.Item name="quota_min" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber 
                                    placeholder="最小值" 
                                    min={0} 
                                    step={0.000001}
                                    precision={6}
                                    style={{ width: '100%' }} 
                                />
                            </Form.Item>
                            <Form.Item name="quota_max" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber 
                                    placeholder="最大值" 
                                    min={0} 
                                    step={0.000001}
                                    precision={6}
                                    style={{ width: '100%' }} 
                                />
                            </Form.Item>
                        </Input.Group>
                    </Form.Item>
                </Col>

                <Col span={12}>
                    <Form.Item label={t('logsTable.requestDuration') + " 范围 (秒)"}>
                        <Input.Group compact>
                            <Form.Item name="request_duration_min" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber placeholder="最小值" min={0} step={0.1} style={{ width: '100%' }} />
                            </Form.Item>
                            <Form.Item name="request_duration_max" style={{ width: '50%', marginBottom: 0 }}>
                                <InputNumber placeholder="最大值" min={0} step={0.1} style={{ width: '100%' }} />
                            </Form.Item>
                        </Input.Group>
                    </Form.Item>
                </Col>
            </Row>

            <Row gutter={16}>
                <Col span={12}>
                    <Form.Item label={t('logsTable.requestId')} name="request_id">
                        <Input placeholder={t('logsTable.requestIdPlaceholder')} />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item label={t('logsTable.errorCode')} name="error_code">
                        <AutoComplete
                            options={AUTOCOMPLETE_ERROR_CODES.map(code => ({value: code}))}
                            placeholder={t('logsTable.errorCodePlaceholder')}
                            allowClear
                        />
                    </Form.Item>
                </Col>
            </Row>

            {/* 排除错误码 */}
            <Form.Item label={t('logsTable.excludeErrorCodes')} name="exclude_error_codes">
                <Select
                    mode="tags"
                    placeholder={t('logsTable.excludeErrorCodesPlaceholder')}
                    options={AUTOCOMPLETE_ERROR_CODES.map(code => ({value: code, label: code}))}
                    allowClear
                    showSearch
                    filterOption={(input, option) => {
                        if (typeof option?.label === 'string') {
                            return option.label.toLowerCase().includes(input.toLowerCase());
                        }
                        return false;
                    }}
                />
            </Form.Item>

            {/* 排序选项 */}
            <Form.Item label={t('logsTable.sortOrder')} name="sort_order">
                <Select placeholder={t('logsTable.selectSortOrder')} allowClear>
                    <Select.Option value="desc">{t('logsTable.newestFirst')}</Select.Option>
                    <Select.Option value="asc">{t('logsTable.oldestFirst')}</Select.Option>
                </Select>
            </Form.Item>

            <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                <Button onClick={handleReset} icon={<ClearOutlined />}>
                    {t('common.reset')}
                </Button>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    {t('common.search')}
                </Button>
            </div>
        </Form>
    );

    // 列设置组件
    const ColumnSettings = () => {
        const allColumns = isAdmin(userState) ? getAdminColumns() : getUserColumns();
        
        return (
            <div style={{ padding: '16px' }}>
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <strong>{t('logsTable.columnSettings')}</strong>
                    <Space>
                        <Button 
                            size="small" 
                            onClick={() => {
                                const newVisible = {};
                                allColumns.forEach(col => {
                                    const key = col.key || col.dataIndex;
                                    if (key) newVisible[key] = true;
                                });
                                setVisibleColumns(newVisible);
                                saveVisibleColumns(newVisible);
                            }}
                        >
                            全选
                        </Button>
                        <Button 
                            size="small" 
                            onClick={() => {
                                const newVisible = {};
                                allColumns.forEach(col => {
                                    const key = col.key || col.dataIndex;
                                    if (key) newVisible[key] = false;
                                });
                                setVisibleColumns(newVisible);
                                saveVisibleColumns(newVisible);
                            }}
                        >
                            全不选
                        </Button>
                    </Space>
                </div>
                
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                    {allColumns.map(col => {
                        const key = col.key || col.dataIndex;
                        if (!key) return null;
                        
                        const isVisible = visibleColumns[key] !== false;
                        
                        return (
                            <div key={key} style={{ 
                                marginBottom: '8px', 
                                display: 'flex', 
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '4px 8px',
                                borderRadius: '4px',
                                backgroundColor: isVisible ? '#f6ffed' : '#fff2f0'
                            }}>
                                <Checkbox
                                    checked={isVisible}
                                    onChange={(e) => handleColumnVisibleChange(key, e.target.checked)}
                                >
                                    {col.title}
                                </Checkbox>
                                {isVisible ? 
                                    <EyeOutlined style={{ color: '#52c41a' }} /> : 
                                    <EyeInvisibleOutlined style={{ color: '#ff4d4f' }} />
                                }
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    // 统计图表组件
    const items = [
        {
            key: '1',
            label: (
                <div style={{display: 'flex', alignItems: 'center', gap: '4px'}}>
                    {t('logsTable.dailyModelUsageStats')}
                    <Tooltip
                        title={
                            <div>
                                <div>{t('logsTable.timezoneNote', {
                                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                                })}</div>
                                <div style={{marginTop: '8px'}}>{t('logsTable.timezoneDescription')}</div>
                                <Button
                                    type="link"
                                    size="small"
                                    onClick={() => navigate('/account/profile')}
                                    style={{padding: '4px 0'}}
                                >
                                    {t('logsTable.goToProfile')} →
                                </Button>
                            </div>
                        }
                    >
                        <InfoCircleOutlined style={{fontSize: '14px', color: '#1677ff'}}/>
                    </Tooltip>
                </div>
            ),
            children: (
                <Row gutter={[24, 24]}>
                    <Col span={isMobile() ? 24 : 14}>
                        <DailyUsageByModelEcharts
                            searchParams={searchParams}
                            startTimestamp={startTime?.unix()}
                            endTimestamp={endTime?.unix()}
                            tokenName={searchForm.getFieldValue('token_name')}
                            username={searchForm.getFieldValue('username')}
                            modelName={searchForm.getFieldValue('model_name')}
                            channel={searchForm.getFieldValue('channel')}
                            channelName={searchForm.getFieldValue('channel_name')}
                            granularityParam={searchForm.getFieldValue('dimension') || finalUsageStatsDefaultTimeUnit}
                        />
                    </Col>
                    <Col span={isMobile() ? 24 : 10}>
                        <div style={{height: '240px', width: '100%'}}>
                            <Spin
                                tip={t('logsTable.modelUsage')}
                                indicator={<LoadingOutlined style={{fontSize: 24}} spin/>}
                                spinning={loadingModelUsageData}
                            >
                                {modelUsageData && modelUsageData.length > 0 ? (
                                    <MemoizedPie data={modelUsageData}/>
                                ) : (
                                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{marginTop: 65}}/>
                                )}
                            </Spin>
                        </div>
                    </Col>
                </Row>
            )
        }
    ];

    return (
        <>
            {/* 统计图表折叠面板 */}
            <Collapse
                style={{marginBottom: 15}}
                defaultActiveKey={[(customShowDailyModelUsageStats) ? '1' : '']}
                onChange={async () => {
                    if (customShowDailyModelUsageStats) {
                        setCustomShowDailyModelUsageStats(false);
                        localStorage.setItem('customShowDailyModelUsageStats', 'false');
                    } else {
                        setCustomShowDailyModelUsageStats(true);
                        localStorage.setItem('customShowDailyModelUsageStats', 'true');
                        await fetchStatsData(lastParams || {}, true);
                    }
                }}
                items={items}
            />

            {/* 工具栏 */}
            <Card 
                size="small" 
                style={{ marginBottom: 16 }}
                bodyStyle={{ padding: '12px 16px' }}
            >
                {/* 第一行：统计数据 */}
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '12px'
                }}>
                    <div style={{ flex: 1, minWidth: '300px' }}>
                        {needGetSata ? (
                            <ConsumptionMetricsPanel
                                isLoading={loadingStats}
                                isMobile={isMobile()}
                                totalConsumption={renderQuota(stat.quota)}
                                metrics={{
                                    rpm: stat.rpm || 0,
                                    tpm: stat.tpm || 0,
                                    mpm: stat.mpm || 0,
                                    dailyEstimate: stat.mpm * 60 * 24
                                }}
                                onRefresh={refreshRpmStats}
                                onViewTotal={handleViewTotalQuota}
                                isLoadingTotal={loadingTotalQuota}
                                isRealtime={stat.is_realtime_data}
                                autoRefreshInterval={autoRefreshInterval}
                                onChangeAutoRefresh={handleChangeAutoRefresh}
                                t={t}
                            />
                        ) : (
                            <Button
                                type='text'
                                onClick={async () => {
                                    setNeedGetSata(true);
                                    if (lastParams) {
                                        isAdmin(userState) ? await getLogStat(lastParams, true) : await getLogSelfStat(lastParams, true);
                                    }
                                }}
                            >
                                {t('logsTable.statsData')}
                            </Button>
                        )}
                    </div>

                    <Button
                        type="text"
                        icon={customShowDailyModelUsageStats ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                        onClick={async () => {
                            if (customShowDailyModelUsageStats) {
                                setCustomShowDailyModelUsageStats(false);
                                localStorage.setItem('customShowDailyModelUsageStats', 'false');
                            } else {
                                setCustomShowDailyModelUsageStats(true);
                                localStorage.setItem('customShowDailyModelUsageStats', 'true');
                                await fetchStatsData(lastParams || {}, true);
                            }
                        }}
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px'
                        }}
                    >
                        {customShowDailyModelUsageStats ? '隐藏统计图' : '显示统计图'}
                    </Button>
                </div>

                {/* 第二行：操作按钮 */}
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    flexWrap: 'wrap',
                    gap: '8px'
                }}>
                    {/* 左侧：表格模式切换 */}
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        {setUseResizableTable && (
                            <div style={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                gap: '8px',
                                height: '32px',
                                padding: '0 12px',
                                borderRadius: '6px',
                                backgroundColor: '#fafafa',
                                border: '1px solid #d9d9d9'
                            }}>
                                <span style={{ 
                                    fontSize: '14px', 
                                    color: '#666', 
                                    fontWeight: 500,
                                    whiteSpace: 'nowrap'
                                }}>
                                    表格模式:
                                </span>
                                <Tooltip 
                                    title={
                                        useResizableTable 
                                            ? "使用支持拖拽调整列宽的表格，功能丰富可拖拽" 
                                            : "使用标准ProTable，功能完整稳定"
                                    }
                                >
                                    <Switch 
                                        checked={useResizableTable}
                                        onChange={setUseResizableTable}
                                        checkedChildren="拖拽"
                                        unCheckedChildren="标准"
                                        style={{ 
                                            minWidth: 72,
                                            fontSize: '12px'
                                        }}
                                    />
                                </Tooltip>
                                <Badge 
                                    count="Pro" 
                                    style={{ 
                                        backgroundColor: '#52c41a',
                                        fontSize: '10px',
                                        lineHeight: '16px',
                                        height: '18px',
                                        minWidth: '32px'
                                    }}
                                />
                            </div>
                        )}
                    </div>

                    {/* 右侧：操作按钮组 */}
                    <div style={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        gap: '6px', 
                        alignItems: 'center'
                    }}>
                        <UserSettingsComponent />
                        
                        <Button
                            icon={<SearchOutlined />}
                            onClick={() => setSearchVisible(true)}
                            size="small"
                        >
                            高级搜索
                        </Button>
                        
                        <Button
                            icon={<SettingOutlined />}
                            onClick={() => setColumnsVisible(true)}
                            size="small"
                        >
                            列设置
                        </Button>
                        
                        <Button
                            icon={<ReloadOutlined />}
                            onClick={() => {
                                const params = getSearchParams();
                                loadData(params);
                            }}
                            size="small"
                            loading={loading}
                        >
                            刷新
                        </Button>
                        
                        {isAdmin(userState) && (
                            <Select
                                defaultValue={["2"]}
                                onChange={handleChangeLogDurationType}
                                style={{ width: 100 }}
                                size="small"
                            >
                                <Select.Option value="1">请求耗时</Select.Option>
                                <Select.Option value="2">首字节耗时</Select.Option>
                                <Select.Option value="3">总耗时</Select.Option>
                                <Select.Option value="0">全部展示</Select.Option>
                            </Select>
                        )}
                        
                        <Popconfirm title={t('logsTable.exportConfirm')} onConfirm={downloadCSV}>
                            <Button 
                                icon={<FileExcelFilled/>} 
                                size="small"
                            >
                                导出
                            </Button>
                        </Popconfirm>
                    </div>
                </div>
            </Card>

            {/* 主表格 */}
            <Card 
                size="small"
                bodyStyle={{ padding: 0 }}
            >
                <Table
                    columns={getColumns}
                    dataSource={logs}
                    rowKey="id"
                    loading={loading}
                    pagination={pagination}
                    onChange={handleTableChange}
                    scroll={{ x: 'max-content' }}
                    size="small"
                    components={{
                        header: {
                            cell: ResizableTitle,
                        },
                    }}
                    locale={{
                        emptyText: (
                            <Empty 
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="暂无数据"
                            />
                        )
                    }}
                />
            </Card>

            {/* 高级搜索抽屉 */}
            <Drawer
                title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <FilterOutlined />
                        高级搜索
                    </div>
                }
                open={searchVisible}
                onClose={() => setSearchVisible(false)}
                width={600}
                extra={
                    <Space>
                        <Button onClick={handleReset} size="small">
                            重置
                        </Button>
                        <Button 
                            type="primary" 
                            onClick={() => searchForm.submit()} 
                            size="small"
                            icon={<SearchOutlined />}
                        >
                            搜索
                        </Button>
                    </Space>
                }
            >
                <SearchForm />
            </Drawer>

            {/* 列设置抽屉 */}
            <Drawer
                title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <SettingOutlined />
                        列显示设置
                    </div>
                }
                open={columnsVisible}
                onClose={() => setColumnsVisible(false)}
                width={400}
            >
                <ColumnSettings />
            </Drawer>

            {/* 内容详情弹窗 */}
            <LogContentDetail
                visible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                content={currentContent}
            />

            {/* 请求日志详情弹窗 */}
            <Modal
                title={`请求日志详情 (${currentRequestId})`}
                open={isRequestLogsModalVisible}
                onCancel={() => setIsRequestLogsModalVisible(false)}
                width={1200}
                footer={null}
            >
                <Spin spinning={loadingRequestLogs}>
                    {requestLogs.length > 0 ? (
                        <Table
                            columns={getColumns}
                            dataSource={requestLogs}
                            rowKey="id"
                            pagination={false}
                            size="small"
                        />
                    ) : (
                        <Empty description="无相关请求日志"/>
                    )}
                </Spin>
            </Modal>

            {/* 日志详情抽屉 */}
            {isAdmin(userState) && (
                <LogDetailDrawer
                    isLogDetailDrawerVisible={isLogDetailDrawerVisible}
                    setIsLogDetailDrawerVisible={setIsLogDetailDrawerVisible}
                    showLogDetailDrawerLogId={showLogDetailDrawerLogId}
                    setShowLogDetailDrawerLogId={setShowLogDetailDrawerLogId}
                />
            )}
        </>
    );
};

export default ResizableLogsTable; 